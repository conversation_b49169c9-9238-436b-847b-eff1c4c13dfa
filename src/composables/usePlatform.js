import { ref, onMounted, computed, readonly } from 'vue'
import { 
  detectPlatform, 
  getDefaultReferralPlatform, 
  PLATFORM_TYPES 
} from '@/utils/platformDetection'

/**
 * Vue composable for platform detection
 * Provides reactive platform information throughout the application
 */
export function usePlatform() {
  const platform = ref(PLATFORM_TYPES.UNKNOWN)
  const isLoading = ref(true)

  const updatePlatformInfo = () => {
    const detectedPlatform = detectPlatform()
    
    platform.value = detectedPlatform
    isLoading.value = false
    
    console.log('Platform detected:', detectedPlatform)
  }

  // Computed properties for easy access
  const isInLIFF = computed(() => platform.value === PLATFORM_TYPES.LIFF)
  const isInTelegram = computed(() => platform.value === PLATFORM_TYPES.TELEGRAM)
  const isInKaia = computed(() => platform.value === PLATFORM_TYPES.WEB)
  const isUnknown = computed(() => platform.value === PLATFORM_TYPES.UNKNOWN)

  // Default referral platform
  const defaultReferralPlatform = computed(() => getDefaultReferralPlatform())

  onMounted(() => {
    updatePlatformInfo()
  })

  return {
    // Platform state
    platform: readonly(platform),
    isLoading: readonly(isLoading),
    
    // Platform checks
    isInLIFF,
    isInTelegram,
    isInKaia,
    isUnknown,
    
    // Default platform
    defaultReferralPlatform,
    
    // Methods
    updatePlatformInfo,
    detectPlatform,
    getDefaultReferralPlatform
  }
} 