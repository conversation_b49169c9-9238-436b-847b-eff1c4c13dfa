<script setup lang="ts">
    const props = defineProps<{
        clickable?: boolean
        iconSrc?: string
        showArrow?: boolean
    }>()
</script>

<template>
    <div :class="['menu-panel', { clickable }]">
        <img v-if="iconSrc" class="panel-icon" :src="iconSrc" />
        <slot></slot>
        <div class="right-content">
            <slot name="right">
                <img v-if="showArrow" class="arrow-icon" src="/ui/navigate-arrow-2.png" />
            </slot>
        </div>
    </div>
</template>

<style scoped>
.menu-panel {
    height: calc(var(--base-unit) * 32);
    padding: calc(var(--base-unit) * 6) calc(var(--base-unit) * 12);

    background: rgba(231, 213, 203, 1);
    border: solid calc(var(--base-unit) * 3) rgba(212, 183, 167, 1);
    border-radius: calc(var(--base-unit) * 12);
    
    color: rgba(75, 89, 116, 1);
    -webkit-text-stroke: 0;
    text-shadow: none;

    display: flex;
    align-items: center;
    gap: calc(var(--base-unit) * 12);
    position: relative; /* Needed for absolute positioning */
    transition: transform 0.2s ease;
}

.menu-panel.clickable:active {
    transform: scale(0.95);
}

.right-content {
    display: flex;
    align-items: center;
    gap: calc(var(--base-unit) * 4);
    margin-left: auto; /* Pushes it to the right naturally */
}

.panel-container {
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
}

.arrow-icon {
    width: calc(var(--base-unit) * 20);
    height: calc(var(--base-unit) * 20);
    object-fit: contain;
}

.panel-icon {
    width: calc(var(--base-unit) * 28);
    height: calc(var(--base-unit) * 28);
    object-fit: contain;
}
</style>