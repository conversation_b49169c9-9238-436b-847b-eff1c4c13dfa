<script setup lang="ts">
import { AppModal, CloseButton } from '@/components/common'

const props = defineProps<{
  isOpen: boolean
}>()

defineSlots<{
  title?: () => any
  modal?: () => any
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const handleOverlayClick = (e: MouseEvent) => {
  if ((e.target as HTMLElement).classList.contains('popup-overlay')) {
    emit('close')
  }
}
</script>


<template>
  <div class="popup-overlay" v-if="isOpen" @click="handleOverlayClick">
    <div class="popup-container" @click.stop>
      <slot name="modal">
        <AppModal class="info-panel">
          <div class="popup-header">
            <div class="popup-title">
              <slot name="title"></slot>
            </div>
            <CloseButton class="close-button" @click="emit('close')" />
          </div>
          <div class="popup-content">
            <slot></slot>
          </div>
        </AppModal>
      </slot>
    </div>
  </div>
</template>

<style scoped>
.popup-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.info-panel {
  margin: calc(var(--base-unit) * 20);
}

.popup-container {
  animation: slideUp 0.4s ease-out;
  width: 100%;
  position: relative;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 16);
}

.popup-title {
  width: 100%;
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.popup-content {
  text-align: left;
  padding: calc(var(--base-unit) * 8);
}

.close-button {
  position: absolute;
  top: calc(var(--base-unit) * 5);
  right: calc(var(--base-unit) * 5);
  z-index: 10;
}
</style>
