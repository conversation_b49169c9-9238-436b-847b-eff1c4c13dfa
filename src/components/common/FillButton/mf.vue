<template>
  <button
    class="progress-container"
    :class="[customClass, { ready }]"
    @click="onClick"
  >
    <div class="progress-bar">
      <div
        class="progress-fill"
        :style="{ width: `${progressPercent * 100}%` }"
      />
    </div>
    <div class="time-text" :class="{ flash: timeFlash }">
      <slot />
    </div>
  </button>
</template>

<script setup>
import { defineProps } from 'vue'
const props = defineProps({
  ready: <PERSON>olean,
  progressPercent: {
    type: Number,
    default: 0
  },
  timeFlash: Boolean,
  customClass: {
    type: [String, Array, Object],
    default: ''
  },
  onClick: Function
})
</script>

<style scoped>
.progress-container {
  margin: calc(var(--base-unit) * -22) auto calc(var(--base-unit) * -8) auto; 
  position: relative;
  width: calc(var(--base-unit) * 110);
  height: calc(var(--base-unit) * 30);
  background: #FF8A28;
  border-radius: calc(var(--base-unit) * 10);
  padding: calc(var(--base-unit) * 2);
  border: calc(var(--base-unit) * 2.5) solid #252635;
  overflow: hidden;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.progress-container.ready {
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.progress-container.ready:hover {
  transform: scale(1.02);
}

.progress-container.ready:active {
  transform: scale(0.98);
}

.progress-bar {
  flex: 1;
  height: 100%;
  background: transparent;
}

.progress-fill {
  height: 100%;
  background: #FDC844;
  transition: width 1s linear;
  border-radius: calc(var(--base-unit) * 8);
}

.time-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -45%);
  z-index: 1;
  color: #FFFFFF;
  font-size: calc(var(--base-unit) * 12);
  -webkit-text-stroke: calc(var(--base-unit) * 2) #3B3B3B;
  text-shadow: 0 calc(var(--base-unit) * 2) calc(var(--base-unit) * 5) rgba(0, 0, 0, 0.32);
  font-weight: bold;
  white-space: nowrap;
}

@keyframes flashGrow {
  0% { color: #5EFF5C; transform: translate(-50%, -45%) scale(1); }
  50% { transform: translate(-50%, -45%) scale(1.3); }
  100% { color: #FFFFFF; transform: translate(-50%, -45%) scale(1); }
}

.flash {
  animation: flashGrow 0.35s ease-in-out;
}
</style>
