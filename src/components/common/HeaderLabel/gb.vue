<script>
export default {
name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
props: {
    text: {
    type: String,
    default: 'Default Label Text'
    }
}
}
</script>

<template>
      <div class="banner-container">
        <div class="banner-content">
          <div class="main-text"> {{ text }}</div>
        </div>
      </div>
</template>

  
<style scoped>
.banner-container {
  position: relative;
  display: flex;
  justify-content: center;
}

.banner-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(var(--base-unit) * 180);
  height: calc(var(--base-unit) * 32);
  background: url('/ui/header-label.svg') no-repeat;
  background-size: contain;
}

.main-text {
  font-size: calc(var(--base-unit) * 16);
  color: #FFEFBD;
  -webkit-text-stroke: calc(var(--base-unit) * 2) #7F521A;
  text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) #7F521A;
  position: absolute;
}
</style>