<template>
    <div class="tabs-container">
      <div 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-wrapper"
      >
        <div 
          :class="['tab', { 'active': activeTab === index }]"
          @click="setActiveTab(index)"
        >
          {{ tab.title }}
        </div>
        <!-- Add the divider after each tab except the last one -->
        <div v-if="index < tabs.length - 1" class="tab-divider"></div>
      </div>
    </div>

  </template>
  
  <script>
import audioService from '@/lib/audioService'

  export default {
    name: 'TabsLayout',
    props: {
      initialTab: {
        type: Number,
        default: 0
      },
      tabs: {
        type: Array,
        default: () => [
          { title: 'Tab 01' },
          { title: 'Tab 02' },
          { title: 'Tab 03' }
        ]
      }
    },
    data() {
      return {
        activeTab: this.initialTab
      }
    },
    methods: {
      setActiveTab(index) {
        audioService.play('button1'); // Play sound effect
        this.activeTab = index;
        this.$emit('tab-change', index);
      }
    }
  }
  </script>
  
  <style scoped>
  .tabs-container {
    display: flex;
    background-color: rgba(53, 54, 75, 1);
    border-radius: calc(var(--base-unit) * 6);
    padding: calc(var(--base-unit) * 2);
    gap: calc(var(--base-unit) * 4);
    align-items: center;

  }
  
  .tab-wrapper {
    display: flex;
    flex: 1;
    align-items: center;
  }
  
  .tab {
    flex: 1;
    text-align: center;
    padding: calc(var(--base-unit) * 4) 0;
    font-weight: bold;
    color: white;
    border-radius: calc(var(--base-unit) * 6);
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin: 0 calc(var(--base-unit) * 4);
    user-select: none;
    font-size: calc(var(--base-unit) * 12);
    padding-top: calc(var(--base-unit) * 6);

  }
  
  .tab-divider {
    width: calc(var(--base-unit) * 1);
    height: calc(var(--base-unit) * 12);
    background-color: rgba(150, 150, 150, 0.5);
    margin: 0 calc(var(--base-unit) * 4);
  }
  
  .tab.active {
    background-color: rgba(114, 221, 32, 1);
    color: white;
    border: solid calc(var(--base-unit) * 2) rgba(37, 38, 53, 1);
    -webkit-text-stroke: calc(var(--base-unit) * 2) rgba(64, 127, 16, 1);
  }

  </style>