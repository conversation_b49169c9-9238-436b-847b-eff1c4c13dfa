<template>
  <div class="data-table">
    <div class="table-header">
      <div
        v-for="(column, index) in columns"
        :key="index"
        class="header-cell"
        :class="column.class"
        :style="{ width: column.width }"
      >
        {{ column.title }}
      </div>
    </div>

    <div class="table-body">
      <div
        v-for="(row, rowIndex) in data"
        :key="rowIndex"
        class="table-row"
      >
        <div
          v-for="(column, colIndex) in columns"
          :key="colIndex"
          class="cell"
          :class="column.class"
          :style="{ width: column.width }"
        >
          <div class="cell-content">
            {{ row[column.key] }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue'

interface Column {
  title: string
  key: string
  width?: string
  class?: string
}

defineProps<{
  columns: Column[]
  data: Record<string, any>[]
}>()
</script>

<style scoped>
.data-table {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 8);
  max-height: calc(var(--base-unit) * 400);
  overflow-y: auto;
}

.table-header {
  display: flex;
  background-color: rgba(45, 114, 193, 1);
  color: white;
  font-weight: bold;
  padding: calc(var(--base-unit) * 10) calc(var(--base-unit) * 8);
  font-size: calc(var(--base-unit) * 12);
  border-radius: calc(var(--base-unit) * 8);
  margin-bottom: calc(var(--base-unit) * 4);
  box-shadow: 0 calc(var(--base-unit) * 2) calc(var(--base-unit) * 3) rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1;
}

.header-cell {
  overflow: visible;
  text-overflow: clip;
  white-space: normal;
  word-wrap: break-word;
  hyphens: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(var(--base-unit) * 20);
  text-align: center;
  line-height: 1.3;
}

.table-body {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 8);
  overflow-y: auto;
}

.table-row {
  display: flex;
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 8);
  font-size: calc(var(--base-unit) * 14);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: calc(var(--base-unit) * 8);
  box-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 3) rgba(0, 0, 0, 0.08);
}

.table-row:nth-child(even) {
  background-color: rgba(186, 200, 221, 1);
}

.table-row:nth-child(odd) {
  background-color: rgba(214, 222, 235, 1);
}

.cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  color: rgba(75, 89, 116, 1);
}

.cell-content::-webkit-scrollbar {
  height: calc(var(--base-unit) * 3);
}

.cell-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: calc(var(--base-unit) * 2);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.with-padding-left {
  padding-left: calc(var(--base-unit) * 5);
}
</style>
