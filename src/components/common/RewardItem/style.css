.reward-item {
    flex-shrink: 0;
    border-radius: calc(var(--base-unit) * 12);
    width: calc(var(--base-unit) * 40);
    height: calc(var(--base-unit) * 40);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--base-unit) * 1);
    position: relative;
}

.reward-amount {
    position: absolute;
    color: #FFF;
    font-size: calc(var(--base-unit) * 10);
    bottom: calc(var(--base-unit) * 3);
}

.reward-item img {
    width: calc(var(--base-unit) * 28);
    height: calc(var(--base-unit) * 28);
}

.disabled-reward {
    filter: grayscale(100%);
}