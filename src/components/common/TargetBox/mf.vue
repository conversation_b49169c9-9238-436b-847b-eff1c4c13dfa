<template>
  <div class="target-box">
    <slot />
  </div>
</template>

<script setup lang="ts">
// No props needed for now; content is passed via slot
</script>

<style scoped>
.target-box {
  display: flex;
  align-items: center;
  width: calc(var(--base-unit) * 65);
  height: calc(var(--base-unit) * 45);
  padding: calc(var(--base-unit) * 4);
  background-color: rgba(251, 223, 99, 1);
  border-radius: calc(var(--base-unit) * 4);
  box-shadow: inset 0 0 0 calc(var(--base-unit) * 2) rgba(254, 246, 210, 1);
  color: rgba(153, 98, 0, 1);
  font-size: calc(var(--base-unit) * 10);
  line-height: calc(var(--base-unit) * 14);
  text-align: center;
  justify-content: center;
}
</style>
