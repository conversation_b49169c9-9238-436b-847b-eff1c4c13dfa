import { define<PERSON>hain } from '@reown/appkit/networks'

export const pharosMainnet = defineChain({
  id: 1000000,
  name: 'Pharos Mainnet',
  nativeCurrency: { 
    name: '<PERSON><PERSON><PERSON>', 
    symbol: 'PHRS', 
    decimals: 18 
  },
  rpcUrls: {
    default: {
      http: ['https://mainnet.dplabs-internal.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'PharosScan',
      url: 'https://pharosscan.xyz',
    },
  },
}) 