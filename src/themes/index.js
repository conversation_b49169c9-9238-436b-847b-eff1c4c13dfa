// Centralize all theme color variables in mfTheme.js and gbTheme.js for easy management.
import mf from './mf';
import gb from './gb';

import { computed } from 'vue'
import { useTheme } from './useTheme'

// THEME ASSETS HOOK - Returns the current theme's assets object
export function useThemeAssets() {
  const { themeName } = useTheme();
  const themeRegistry = {
    gb: { assets: gb },
    mf: { assets: mf },
  };
  
  return computed(() => {
    const theme = themeRegistry[themeName.value] || themeRegistry['mf'];
    return theme.assets;
  });
}