import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager';
import { useThemeAssets } from '@/themes';
import { setupCookieTest } from '@/utils/cookieTest';

import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import { useNetworkSwitchHandler } from './features/auth/composables/useNetworkSwitchHandler';

// Safari viewport height fix
function setViewportHeight() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// Set initial viewport height
setViewportHeight();

// Update viewport height on resize and orientation change
window.addEventListener('resize', setViewportHeight);
window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100);
});

window.addEventListener('load', async function () {
    try {
        // Get theme name directly from environment variables
        const isDevelopment = import.meta.env.DEV;
        const themeName = isDevelopment
            ? (import.meta.env.VITE_THEME || 'mf')
            : (import.meta.env.VITE_PROD_THEME || 'mf');

        // Load locale messages first
        await loadLocaleMessages(i18n.global.locale.value, themeName);

        // Initialize AppKit for gb theme
        if(themeName === 'gb') {
            createAppKit({
                adapters: [new Ethers5Adapter()],
                networks: [defaultNetwork],
                projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
                metadata: {
                    name: 'GooseBox',
                    description: 'GooseBox Game',
                    url: window.location.origin,
                    icons: ['https://avatars.githubusercontent.com/u/37784886'],
                },
            })
        }

        // Create and mount the Vue app
        const app = createApp(App);
        app.use(i18n).use(createPinia()).use(router);
        app.mount('#app');

        // Now that Vue app is mounted and Pinia is active, we can safely use stores
        const gameplayStateManager = useGameplayStateManager();
        gameplayStateManager.enterGameplay();

        // Initialize network switch handler
        const networkSwitchHandler = useNetworkSwitchHandler();
        networkSwitchHandler.handleNetworkEvents();

        // Apply theme after Vue app is mounted
        const themeAssets = useThemeAssets();
        applyTheme(themeAssets.value);

        // Ensure final sync on app exit
        window.addEventListener('beforeunload', async () => {
            await gameplayStateManager.exitGameplay();
        });

        // Initialize LIFF
        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

        // 在开发环境中设置cookie测试工具
        if (isDevelopment) {
            setupCookieTest()
        }

    } catch (error) {
        console.error("Error initializing application:", error);
    }
});
