import { ref } from "vue";


class AudioService {
  constructor() {
    this.isMuted = ref(localStorage.getItem('app_muted') === 'true');

    // SFX
    this.sounds = {
      button1: new Audio('/audio/sfx/button1.wav'),
      button2: new Audio('/audio/sfx/button2.wav')
    };
    
    // Set preload manually (optional, but good for clarity)
    this.sounds.button1.preload = 'auto';
    this.sounds.button2.preload = 'auto';
    
    // Optionally: Load it immediately
    this.sounds.button1.load();
    this.sounds.button2.load();
    

    // BGM
    this.bgm = new Audio('/audio/bgm/main.wav');
    this.bgm.loop = true;
    this.bgm.volume = this.isMuted.value ? 0 : 0.2;
  }

  getMute() {
    return this.isMuted.value;
  }

  setMute(set) {
    this.isMuted.value = set;
    localStorage.setItem('app_muted', set);
    if (set) {
      this.bgm.pause();
      this.bgm.volume = 0;
    } else {
      this.bgm.volume = 0.2;
      this.bgm.play().catch(err => console.error('Error resuming BGM:', err));
    }
  }

  // 🔇 Toggle Mute (affects both SFX and BGM)
  toggleMute() {
    this.setMute(!this.getMute())
  }

  // 🔊 Play SFX (only if not muted)
  play(soundName) {
    if(this.getMute()) return;

    const baseSound = this.sounds[soundName];
    if (baseSound) {
      const sound = baseSound.cloneNode(); // make a fresh copy
      sound.currentTime = 0;
      sound.play().catch(err => console.error(`Error playing SFX "${soundName}":`, err));
    }
    
  }

  // 🎶 Play BGM (only if not muted)
  playBGM() {
    if(this.getMute()) return;
    this.bgm.volume = 0.2;
    //this.bgm.play().catch(err => console.error('Error playing BGM:', err));
  }

}

const audioService = new AudioService();
export default audioService;
