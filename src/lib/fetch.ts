import { useToken, useUser } from '@/features/auth'
import axios from "axios";

const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: { 'Content-Type': 'application/json' },
  withCredentials: true // 启用cookie支持
});

instance.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response?.status === 401) {
      useToken().clearToken()
      useUser().clearUser?.()
    }
    return Promise.reject(error)
  });

instance.interceptors.request.use(
  function (config) {
    const token = localStorage.getItem("token");
    const lang = localStorage.getItem("app_language") || "en"; // default to English

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    config.headers['Accept-Language'] = lang;

    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

export default instance;
