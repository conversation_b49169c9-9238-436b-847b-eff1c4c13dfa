<template>
  <div class="top-section">
    <div class="top">
      <PlayerLabel />
      <ConnectButton />
    </div>
  </div>
</template>

<script setup>
import { PlayerLabel } from '@/features/userInfo'
import { ConnectButton } from '@/features/auth'

</script>

<style scoped>
.top-section {
  position: fixed;
  width: 100%;
  height: calc(var(--base-unit) * 84); 
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 0);
  z-index: 100;
  pointer-events: none;
}

.top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: calc(100% - calc(var(--base-unit) * 32));
  height: calc(var(--base-unit) * 48); 
  padding: calc(var(--base-unit) * 16); 
  pointer-events: none;
}

.top :deep(*) {
  pointer-events: auto;
}
</style>
