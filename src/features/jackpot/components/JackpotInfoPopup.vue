<script setup>
import { TitlePanelColor } from '@/components/common'
import { useJackpotInfoPopupStore } from '../stores/jackpotInfoPopupStore'
import { useI18n } from 'vue-i18n'
import { AppPopup } from '@/components/common'

const { t } = useI18n()
const jackpotInfoPopupStore = useJackpotInfoPopupStore()

  const tiers = [
    {
      level: 'LV1',
      conditions: [
        { labelKey: 'newUserRegistration', amount: '0.01 TON' }
      ],
      reward: '10 TON'
    },
    {
      level: 'LV2',
      conditions: [
        { labelKey: 'newUserRegistration', amount: '0.01 TON' },
        { labelKey: 'openingChest', amount: '0.0001 TON' }
      ],
      reward: '20 TON'
    }
  ];
</script>

<template>
  <AppPopup :isOpen="jackpotInfoPopupStore.isJackpotInfoShow" @close="jackpotInfoPopupStore.closeJackpotInfoPopup">
    <TitlePanelColor class="title">{{ $t('common.jackpotInfo') }}</TitlePanelColor>
    <div class="content-container">
      <p>
        {{ $t('jackpotInfoOverlay.description') }}
      </p>
  
      <ul>
        <li><strong>{{ $t('jackpotInfoOverlay.contribute') }}:</strong> {{ $t('jackpotInfoOverlay.contributeDesc') }}</li>
        <li><strong>{{ $t('jackpotInfoOverlay.trigger') }}:</strong> {{ $t('jackpotInfoOverlay.triggerDesc') }}</li>
        <li><strong>{{ $t('jackpotInfoOverlay.tiers') }}:</strong> {{ $t('jackpotInfoOverlay.tiersDesc') }}</li>
      </ul>
  
      <p class="note">
        {{ $t('jackpotInfoOverlay.note') }}
      </p>
    </div>
  </AppPopup>
</template>

<style scoped>

  .content-container {
    margin-top: calc(var(--base-unit) * 8);
    font-size: calc(var(--base-unit) * 14);
    line-height: 1.6;
    color: #4b5974;
    max-height: calc(var(--base-unit) * 240);
    overflow-y: auto;
    padding-right: calc(var(--base-unit) * 2);
  }
  
  /* Scrollbar */
  .content-container::-webkit-scrollbar {
    width: 6px;
  }
  .content-container::-webkit-scrollbar-thumb {
    background-color: rgba(75, 89, 116, 0.5);
    border-radius: calc(var(--base-unit) * 3);
  }
  
  /* List styling */
  .content-container ul {
    padding-left: calc(var(--base-unit) * 12);
    margin-bottom: calc(var(--base-unit) * 12);
  }
  .content-container li {
    margin-bottom: calc(var(--base-unit) * 2);
  }
  
  .note {
    font-style: italic;
    color: #7a7a7a;
    margin-top: calc(var(--base-unit) * 12);
  }
</style>
