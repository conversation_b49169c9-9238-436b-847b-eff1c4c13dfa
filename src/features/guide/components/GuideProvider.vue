<template>
    <slot />
    <GuideOverlay />
    <GuideStepBubble />
  </template>
  
  <script setup lang="ts">
  import { provide, onMounted, computed } from 'vue';
  import { useGuide, GuideStep } from '../composables/useGuide';
  import GuideOverlay from './GuideOverlay.vue';
  import GuideStepBubble from './GuideStepBubble';
  import guideSteps from '../assets/guideSteps'; // theme-aware steps
  import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine/stores/deliveryLineStore'
  import { useFarmStore } from '@/features/gameplay/farmPlot/stores/farmPlotStore'

  const {
    steps,
    currentStep,
    isActive,
    saveCheckpoint,
    startGuide,
    nextStep,
    endGuide,
    clearCheckpoint
  } = useGuide();
  
  provide('guideSteps', steps);
  provide('guideCurrentStep', computed(() => steps.value[currentStep.value] ?? null));
  provide('guideIsActive', isActive);
  provide('guideNextStep', nextStep);

  const deliveryLineStore = useDeliveryLineStore()
  const farmStore = useFarmStore()
  onMounted( async () => {
    await deliveryLineStore.fetchDeliveryLineFromApi()
    await farmStore.setFarmPlotsFromApi()
    if(farmStore.plots[0].level == 1) clearCheckpoint();

    // skip tutorial if already has progress
    if(farmStore.plots[0].level > 1) return;
    if(farmStore.plots[1].isUnlocked) return;
    if(deliveryLineStore.deliveryLine?.level > 1) return;
    
    steps.value = guideSteps as GuideStep[];
    startGuide();
  });
  </script>
  