<template>
  <div
    v-if="isActive && currentStep"
    class="guide-overlay"
    @click="onOverlayClick"
  >
    <div
      class="highlight-demo"
      v-if="currentStep?.highlightArea"
      :style="highlightStyle"
      @click.stop
    />

    <div
      v-if="currentStep?.selector"
      class="guide-pointer"
      :style="pointerStyle"
    />
  </div>
</template>

<script setup lang="ts">
import { inject, computed, type Ref, ref, watchEffect, nextTick } from 'vue';
import type { GuideStep } from '../composables/useGuide';

const isActive = inject('guideIsActive') as Ref<boolean>;
const currentStep = inject('guideCurrentStep') as Ref<GuideStep | null>;
const nextStep = inject('guideNextStep') as () => void;

function onOverlayClick(e: MouseEvent) {
  const step = currentStep.value;
  if (!step) return;

  if (step.type === 'anywhere') {
    nextStep();
  } else if (step.selector) {
    const el = document.querySelector(step.selector);
    const bounds = el?.getBoundingClientRect();
    if (!bounds) return;

    const { clientX, clientY } = e;
    const isInside =
      clientX >= bounds.left &&
      clientX <= bounds.right &&
      clientY >= bounds.top &&
      clientY <= bounds.bottom;

    if (isInside) {
      // Allow the click to pass through to the target element
      e.stopPropagation();
      e.preventDefault();
      
      // Simulate a click on the target element
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        clientX: clientX,
        clientY: clientY,
      });
      el?.dispatchEvent(clickEvent);
      
      // Advance to next step after successful click
      nextStep();
    } else {
      e.stopPropagation();
      e.preventDefault();
    }
  }
}

const highlightStyle = ref({});

watchEffect(() => {
  const step = currentStep.value;
  if (!step) {
    highlightStyle.value = {};
    return;
  }

  // If no selector, use manual highlight area
  if (!step.selector && step.highlightArea) {
    const area = step.highlightArea;
    highlightStyle.value = {
      top: `calc(var(--base-unit) * ${area.top})`,
      left: `calc(var(--base-unit) * ${area.left})`,
      width: `calc(var(--base-unit) * ${area.width})`,
      height: `calc(var(--base-unit) * ${area.height})`,
      borderRadius: area.radius
        ? `calc(var(--base-unit) * ${area.radius})`
        : 'calc(var(--base-unit) * 4)',
    };
    return;
  }

  // If selector exists, hide the highlight
  if (step.selector) {
    nextTick(() => {
      highlightStyle.value = {
        opacity: 0,
      };
    });
  }
});



const pointerStyle = ref({});

// Helper function to update pointer style with retry logic
const updatePointerStyle = (step: GuideStep, retryCount = 0, maxRetries = 20) => {
  const retryDelay = Math.min(100 + retryCount * 50, 500); // Progressive delay: 100ms, 150ms, 200ms... up to 500ms
  
  const tryUpdate = () => {
    const el = document.querySelector(step.selector!);
    const container = document.querySelector('#app');

    if (!el || !container) {
      if (retryCount < maxRetries) {
        console.log('retry', retryCount);
        setTimeout(() => updatePointerStyle(step, retryCount + 1, maxRetries), retryDelay);
      } else {
        pointerStyle.value = {};
      }
      return;
    }

    el.scrollIntoView({
      behavior: 'auto',
      block: 'center',
      inline: 'center',
    });

    const elRect = el.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    const centerX = elRect.left - containerRect.left + elRect.width / 2;
    const centerY = elRect.top - containerRect.top + elRect.height / 2;

    pointerStyle.value = {
      position: 'absolute',
      top: `${centerY}px`,
      left: `${centerX}px`,
      transform: 'translate(-50%, -50%)',
    };
  };

  if (retryCount === 0) {
    // Initial delay for first attempt
    setTimeout(tryUpdate, 300);
  } else {
    tryUpdate();
  }
};

watchEffect(() => {
  const step = currentStep.value;
  if (!step?.selector) {
    pointerStyle.value = {};
    return;
  }

  nextTick(() => {
    updatePointerStyle(step);
  });
});


</script>

<style scoped>
.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--app-width);
  height: var(--app-height);
  z-index: 9998;
  pointer-events: auto;

}

.highlight-demo {
  position: absolute;
  pointer-events: none;
  box-shadow:
    inset 0 0 calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.5),
    0 0 0 9999px rgba(0, 0, 0, 0.6);
  animation: pulse 2s infinite;
  transition: all 0.3s ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    box-shadow:
      inset 0 0 calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.5),
      0 0 0 9999px rgba(0, 0, 0, 0.6);
  }
  50% {
    box-shadow:
      inset 0 0 calc(var(--base-unit) * 2) rgba(255, 255, 0, 0.3),
      0 0 0 9999px rgba(0, 0, 0, 0.6);
  }
}

.guide-pointer {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(var(--base-unit) * 76);
  height: calc(var(--base-unit) * 76);
  background: url('/ui/pointer.png') no-repeat center center;
  background-size: contain;
  z-index: 9999;
  pointer-events: none;
  animation: pointer-up-down 2s infinite;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease-in-out;
}


@keyframes pointer-up-down {
  0%, 100% {
    transform: translate(-50%, 40%) translateY(0);
  }
  50% {
    transform: translate(-50%, 40%) translateY(-15%);
  }
}

</style>
