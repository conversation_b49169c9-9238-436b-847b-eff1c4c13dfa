<template>
  <div
    v-if="step && step.showBubble !== false"
    class="guide-bubble"
    :style="bubbleStyle"
  >
  <div class="character-container">
    <img v-if="step.image" :src="step.image" class="character" />
  </div>
    <div class="text-box">
      <p>{{ t(step.text) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch, type Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import type { GuideStep } from '../../composables/useGuide';

const { t } = useI18n();
const currentStep = inject('guideCurrentStep') as Ref<GuideStep | null>;

const step = ref<GuideStep | null>(null);
const bubbleStyle = ref<Record<string, string>>({});

watch(currentStep, updatePosition, { immediate: true });

function updatePosition() {
  step.value = currentStep.value;
  if (!step.value) return;

  const position = step.value.bubblePosition ?? 'center';

  if (position === 'center') {
    bubbleStyle.value = {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    };
  } else if (position === 'top') {
    bubbleStyle.value = {
      position: 'fixed',
      top: '10%',
      left: '50%',
      transform: 'translateX(-50%)'
    };
  } else if (position === 'bottom') {
    bubbleStyle.value = {
      position: 'fixed',
      bottom: '10%',
      left: '50%',
      transform: 'translateX(-50%)'
    };
  }
}
</script>

<style scoped>
.guide-bubble {
  position: fixed;
  z-index: 10000;
  display: flex;
  color: #68514B;
  gap: calc(var(--base-unit) * 16); 
  background: #E7D5CB;
  border: calc(var(--base-unit) * 4) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 4);
  min-width: calc(var(--base-unit) * 343);
  box-shadow: 0 calc(var(--base-unit) * 4) calc(var(--base-unit) * 12) rgba(0,0,0,0.15);
  box-sizing: border-box;
  pointer-events: none;
}
.text-box {
  flex: 1;
  font-size: calc(var(--base-unit) * 14);
  align-self: top;
}
.character-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F0DED4;
  border-radius: calc(var(--base-unit) * 8);
  width: calc(var(--base-unit) * 120);
  height: calc(var(--base-unit) * 120);
}

.character {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
