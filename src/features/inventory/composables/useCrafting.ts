import { ref } from 'vue'
import audioService from '@/lib/audioService'
import { useUserInfo } from '@/features/userInfo'

import { useNotificationStore } from '@/features/notification'
import { craftTicketWithFragment } from '../api'

export const useCrafting = (selectedItem) => {
  const isCrafting = ref(false)
  const { fetchUserInfo } = useUserInfo()
  const notificationStore = useNotificationStore()

  const craftTicket = async () => {
    if (!selectedItem.value?.key || isCrafting.value) return

    isCrafting.value = true

    try {
      const fragmentKey = selectedItem.value.key
      const result = await craftTicketWithFragment(fragmentKey)

      if (result) {
        audioService.play('button1')
        await fetchUserInfo()

        notificationStore.addNotification({
          type: 'success',
          message: result.message || 'Crafting succeeded!',
          duration: 3000
        })
      }
    } catch (err) {
      console.error(err)
    } finally {
      isCrafting.value = false
    }
  }

  return {
    isCrafting,
    craftTicket
  }
}
