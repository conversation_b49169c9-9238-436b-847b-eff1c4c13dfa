<script setup lang="ts">
const props = defineProps<{
  description: string | null
  emptyText: string
}>()
</script>

<template>
  <div class="slot slot-description">
    {{ description || emptyText }}
  </div>
</template>

<style scoped>
.slot-description {
  line-height: 1.2;
  padding: calc(var(--base-unit) * 14);
  color: #BCC4C6;
  height: calc(var(--base-unit) * 83);
  border-radius: calc(var(--base-unit) * 12);
  background: linear-gradient(180deg, #1F2553 0%, #232A5A 51%, #3A4479 100%);
  border: calc(var(--base-unit) * 3) solid rgba(13, 34, 89, 1);
  box-sizing: border-box;
}
</style>
