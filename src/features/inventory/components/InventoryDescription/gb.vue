<script setup lang="ts">
const props = defineProps<{
  description: string | null
  emptyText: string
}>()
</script>

<template>
  <div class="slot slot-description">
    {{ description || emptyText }}
  </div>
</template>

<style scoped>
.slot-description {
  line-height: 1.2;
  padding: calc(var(--base-unit) * 14);
  color: #252B37;
  height: calc(var(--base-unit) * 83);
  border-radius: calc(var(--base-unit) * 12);
  box-sizing: border-box;
  background:#D4B7A9;
  border: calc(var(--base-unit) * 2) dashed #0D2259;
  text-align: center;

}
</style>
