import { useThemeAssets } from '@/themes'
import { computed } from 'vue'

export function useGameMenuItems() {
  const currentTheme = useThemeAssets()
  return computed(() => {
    const theme = currentTheme.value
    return [
      { id: 1, name: 'Home', path: '/v1', icon: theme.home },
      { id: 2, name: 'Task', path: '/v1/task', icon: theme.task },
      { id: 3, name: 'Inventory', path: '/v1/inventory', icon: theme.inventory },
      { id: 4, name: 'Referral', path: '/v1/referral', icon: theme.invite },
      { id: 5, name: 'Profile', path: '/v1/profile', icon: theme.profile }
    ]
  })
}
