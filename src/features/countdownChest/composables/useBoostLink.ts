import { ref, Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/features/notification'
import { triggerBoostLink } from '../api'

interface UseBoostLinkReturn {
  isProcessing: Ref<boolean>
  processBoostCode: (code: string) => Promise<boolean>
}

export function useBoostLink(): UseBoostLinkReturn {
  const { t } = useI18n()
  const notificationStore = useNotificationStore()
  const isProcessing = ref(false)

  const processBoostCode = async (code: string): Promise<boolean> => {
    if (!code) return false
    
    isProcessing.value = true
    try {
      await triggerBoostLink(code)
      
      notificationStore.addNotification({
        type: 'success',
        title: t('boost.applied'),
        message: t('boost.codeAppliedSuccess'),
        duration: 3000
      })
      
      return true
    } catch (error) {
      console.error('Failed to process boost code:', error)
      notificationStore.addNotification({
        type: 'error',
        message: t('boost.processingError'),
        duration: 3000
      })
      return false
    } finally {
      isProcessing.value = false
    }
  }

  return {
    isProcessing,
    processBoostCode
  }
} 