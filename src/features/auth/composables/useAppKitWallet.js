// useAppKitWallet.js
import { devLog } from '@/utils/config'

// appkit
import { defaultNetwork } from '@/config/networks'
import { useAppKitProvider, useAppKitNetwork } from '@reown/appkit/vue'
import { ethers } from "ethers";

export const useAppKitWallet = () => {

  const signMessage = async (message, address) => {
    const { walletProvider } = useAppKitProvider("eip155");
    const { chainId } = useAppKitNetwork(defaultNetwork);

    const provider = new ethers.providers.Web3Provider(walletProvider, chainId);
    const signer = provider.getSigner(address);
    console.log(signer);
    const signature = await signer?.signMessage(message);
    console.log(signature);
    return signature;
  };


  return {
    signMessage,
  };
} 