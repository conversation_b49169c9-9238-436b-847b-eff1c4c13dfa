<script setup lang="ts">
import { useReferralReward } from '../composables/useReferralReward'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ColorButton } from '@/components/common'

const {
  isLoading,
  chestCount,
  handleOpenReferralChests
} = useReferralReward()

const { t } = useI18n()
const buttonText = computed(() => {
  return chestCount.value > 0
    ? t('chest.openReferralChests', { count: chestCount.value })
    : t('chest.noChestsAvailable')
})
</script>


<template>
  <ColorButton
    :loading="isLoading"
    :disabled="chestCount <= 0"
    :class="{ 'disabled-button': chestCount <= 0 }"
    @click="handleOpenReferralChests"
  >
    {{ buttonText }}
  </ColorButton>
</template>

<style scoped>
.disabled-button {
  filter: grayscale(100%) brightness(0.6);
  cursor: not-allowed;
}
</style>
