import { GameComponent, Vector2, GameObject } from '@/features/gameplay/shared';
import { TweenBuilder, Easing } from '@/features/gameplay/animation';
import { MilkGameObject } from '@/features/gameplay/deliveryLine/gameObjects/MilkGameObject';
import { MilkRunnerComponent } from '@/features/gameplay/deliveryLine/gameComponents/MilkRunnerComponent';
import { DeliveryLineStats, DeliveryLineTemporalStats, useDeliveryLineStore } from '@/features/gameplay/deliveryLine/stores/deliveryLineStore';
import { Ref } from 'vue';

export class MilkProduceComponent extends GameComponent {
  private timer = 0;
  private onSpawnCallback?: (milk: MilkGameObject) => void;

  constructor(private readonly temporalStats: Ref<DeliveryLineTemporalStats>) {
    super();
  }

  public get blockPrice(): number {
    const deliveryLineStore = useDeliveryLineStore();
    return deliveryLineStore.deliveryLine?.blockPrice ?? 0;
  }

  public get blockUnit(): number {
    const deliveryLineStore = useDeliveryLineStore();
    return deliveryLineStore.deliveryLine?.blockUnit ?? 0;
  }

  public get spawnInterval(): number {
    const deliveryLineStore = useDeliveryLineStore();
    return deliveryLineStore.deliveryLine?.deliverySpeed ?? 0;
  }

  public get remainingMilk(): number {
    const deliveryLineStore = useDeliveryLineStore();
    return Number(deliveryLineStore.deliveryLine?.pendingMilk) + Number(this.temporalStats.value.milkOperations.produce) - Number(this.temporalStats.value.milkOperations.consume);
  }

  setOnMilkSpawned(callback: (milk: MilkGameObject) => void): void {
    this.onSpawnCallback = callback;
  }

  override update(deltaSeconds: number): void {
    this.timer += deltaSeconds;

    if (this.timer >= this.spawnInterval) {
      this.spawnMilk();
      this.timer = 0;
    }
  }

  private async spawnMilk(): Promise<void> {
    if (!this.gameObject) return;
    if (this.remainingMilk < Number(this.blockUnit)) return;

    this.temporalStats.value.milkOperations.consume += Number(this.blockUnit);

    const scene = this.gameObject.scene;
    const milk = await GameObject.instantiate(new MilkGameObject(scene)); 

    milk.transform.position = new Vector2(0.08, 0.67);


    // Add runner component
    const runner = new MilkRunnerComponent(this.blockPrice);
    milk.addComponent(runner);

    this.onSpawnCallback?.(milk);
  }

  override destroy(): void {
    this.timer = 0;
  }
}
