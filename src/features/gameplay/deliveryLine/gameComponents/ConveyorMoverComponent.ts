import { Vector2, GameComponent, GameObject } from '@/features/gameplay/shared';
import { MilkRunnerComponent } from './MilkRunnerComponent';
import { DeliveryLineStats } from '../stores/deliveryLineStore';
import { Ref } from 'vue';

interface TargetData {
  transform: GameObject['transform'];
  milkRunner: MilkRunnerComponent | null;
}

export class ConveyorMoverComponent extends GameComponent {
  private targets: TargetData[] = [];

  constructor(private readonly deliveryLine: Ref<DeliveryLineStats>) {
    super();
  }

  addTarget(transform: GameObject['transform']): void {
    const milkRunner = transform.gameObject.getComponent(MilkRunnerComponent) ?? null;
    this.targets.push({ transform, milkRunner });
  }

  get scrollSpeed(): number {
    return 1 / (this.deliveryLine.value.deliverySpeed * 5);
  }

  override update(deltaSeconds: number): void {
    const moveBy = this.scrollSpeed * deltaSeconds;

    for (const target of this.targets) {
      const pos = target.transform.position;
      pos.x += moveBy; // ✅ In-place mutation, no new Vector2

      if (pos.x > 1 && target.milkRunner) {
        target.milkRunner.onReachedTarget();
        this.targets.splice(this.targets.indexOf(target), 1);
      }
    }
  }

  override destroy(): void {
    this.targets = [];
  }
}
