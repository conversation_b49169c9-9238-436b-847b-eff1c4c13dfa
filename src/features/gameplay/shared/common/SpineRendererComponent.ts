import { Spine, SetupPoseBoundsProvider } from '@esotericsoftware/spine-pixi-v8';
import { GameObject } from '../core/GameObject';
import { Vector2 } from '../generic/Vector2';
import { RendererComponent } from './RendererComponent';

export interface SpineRendererOptions {
  skeletonKey: string,
  atlasKey: string,
  zIndex?: number;
}

export class SpineRendererComponent extends RendererComponent {
  public readonly spine: Spine;
  public scaleRelativeToWidth = true;

  constructor(private options: SpineRendererOptions) {
    super();
    this.spine = Spine.from({ skeleton: options.skeletonKey, atlas: options.atlasKey });
    // Set bounds provider to setup pose for stable scaling
    this.spine.boundsProvider = new SetupPoseBoundsProvider();
    if (options.zIndex !== undefined) this.spine.zIndex = options.zIndex;
  }

  override attach(gameObject: GameObject): void {
    super.attach(gameObject);
    this.gameObject.scene.container.addChild(this.spine);
    this.gameObject.transform.resize();
    this.update();
  }

  override update(): void {
    this.render();
  }

  override render(): void {
    if (!this.spine) return;
    const { scale, position } = this.gameObject.transform;
    this.setSpinePosition(position);
    this.setSpineScale(scale);
  }

  private setSpinePosition(pos: Vector2): void {
    if (this.gameObject.transform.parent) {
      const position = this.gameObject.transform.position;
      const localX = position.x * this.getParentSize().x;
      const localY = position.y * this.getParentSize().y;
      const x = this.getParentPosition().x + localX;
      const y = this.getParentPosition().y + localY;
      this.spine.position.set(x, y);
      return;
    }
    let position = pos.multiply(this.getSceneSize());
    this.spine.position.set(position.x, position.y);
  }

  private setSpineScale(scale: Vector2): void {
    if(this.scaleRelativeToWidth) {
      let size = scale
      .divide(this.getTextureSize().x)
      .multiply(this.getParentSize().x)
      this.spine.scale.set(size.x, size.y);
    }
    else {
      let size = scale
      .divide(this.getTextureSize().y)
      .multiply(this.getParentSize().y)
      this.spine.scale.set(size.x, size.y);
    }
  }
  
  getSpineSize(): Vector2 {
    return new Vector2(this.spine.width, this.spine.height);
  }

  getTextureSize(): Vector2 {
    const bounds = this.spine.getLocalBounds(); // Uses SetupPoseBoundsProvider
    return new Vector2(bounds.width, bounds.height);
  }
  

  getSceneSize(): Vector2 {
    if (!this.gameObject) return new Vector2(0, 0);
    return this.gameObject.scene.getSize();
  }  

  override getRawSize(): Vector2 {
    return new Vector2(this.spine.width, this.spine.height);
  }

  getParentSize(): Vector2 {
    if(!this.gameObject.transform.parent) return this.getSceneSize();
    if(!this.gameObject.transform.parent.gameObject.getComponent(RendererComponent)) return this.getSceneSize();
    return this.gameObject.transform.parent.gameObject.getComponent(RendererComponent).getRawSize();
  }

  override getRawPosition(): Vector2 {
    return new Vector2(this.spine.position.x, this.spine.position.y);
  }

  getParentPosition(): Vector2 {
    if(!this.gameObject.transform.parent) return new Vector2(0, 0);
    if(!this.gameObject.transform.parent.gameObject.getComponent(RendererComponent)) return new Vector2(0, 0);
    return this.gameObject.transform.parent.gameObject.getComponent(RendererComponent).getRawPosition();
  }

  override destroy(): void {
    if (this.spine.parent) {
      this.spine.parent.removeChild(this.spine);
    }
    this.spine.destroy();
  }
} 