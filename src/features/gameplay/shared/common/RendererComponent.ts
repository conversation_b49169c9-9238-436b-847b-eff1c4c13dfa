import { GameComponent } from '../core/GameComponent'
import { Vector2 } from '../generic/Vector2'

export class RendererComponent extends GameComponent {
  render(): void {
    // Optionally overridden by subclasses
  }

  getRawSize(): Vector2 {
    return new Vector2(0, 0) // Default: override in subclass
  }

  getRawPosition(): Vector2 {
    return new Vector2(0, 0) // Default: override in subclass
  }
}
