import { Game<PERSON>omponent, Vector2, Sprite<PERSON><PERSON>er<PERSON>omponent, GameObject } from '@/features/gameplay/shared';
import { TweenBuilder, Easing } from '@/features/gameplay/animation';
import { PopTextGameObject } from '@/features/gameplay/shared';
import { CowBoostComponent } from './CowBoostComponent';
import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine/stores/deliveryLineStore';
import { FarmPlotStats } from '../stores/farmPlotStore';
import { Ref } from 'vue';
import { IndexZSetterComponent } from './IndexZSetterComponent';

export class CowProduceComponent extends GameComponent {
  private elapsedTime = 0;
  private baseScale = new Vector2(1, 1);
  private readonly scaleMultiplier = 1.2;
  private boostComponent: CowBoostComponent | null = null;

  constructor(private readonly plot: Ref<FarmPlotStats>) {
    super();
  }

  get productionSpeed(): number {
    return this.plot.value.productionSpeed;
  }

  override attach(gameObject: import('@/features/gameplay/shared').GameObject): void {
    super.attach(gameObject);
    this.baseScale = gameObject.transform.scale.clone();
    this.elapsedTime = -this.productionSpeed * Math.random();
    
    // Get reference to boost component
    this.boostComponent = gameObject.getComponent(CowBoostComponent);
  }

  override update(deltaSeconds: number): void {
    // Apply boost multiplier to delta time if boosted
    const effectiveDeltaTime = this.boostComponent?.getBoostState() 
      ? deltaSeconds * 2 // 2x speed when boosted
      : deltaSeconds;
    
    this.elapsedTime += effectiveDeltaTime;

    // ⏱ Dynamic interval from production speed (e.g. 5/sec = 0.2s)
    const interval = this.productionSpeed > 0 ? this.productionSpeed : Infinity;

    if (this.elapsedTime >= interval) {
      this.elapsedTime = 0;
      this.produce();
    }
  }

  private async produce(): Promise<void> {
    const scale = this.gameObject.transform.scale;
    const direction = Math.sign(scale.x) || 1;
    const from = direction * this.baseScale.x;
    const to = direction * this.baseScale.x * this.scaleMultiplier;

    // 💬 Show "+milkAmount"
    const text = new PopTextGameObject(this.gameObject.scene, {
      text: `+${(Number(this.plot.value.milk) || 0).toFixed(2)}`,
      scale: new Vector2(0.005, 0.005),
      position: new Vector2(0,0),
      style: {
        fontSize: 52,
        fontWeight: 'bold',
        fill: '#00ff00',
        stroke: {
          color: '#000000',
          width: 6,
        },
      },
    });

    await GameObject.instantiate(text);
    text.textObject.text.zIndex = this.gameObject.getComponent(IndexZSetterComponent)?.zIndex ?? 1;
    text.textObject.transform.parent = this.gameObject.transform;

    TweenBuilder
      .update(scale, (target, value) => {
        target.x = value;
        target.y = Math.abs(value);
      }, from, to, 0.2)
      .setEase(Easing.EaseOutQuad)
      .play();

    TweenBuilder
      .update(scale, (target, value) => {
        target.x = value;
        target.y = Math.abs(value);
      }, to, from, 0.2)
      .setDelay(0.2)
      .setEase(Easing.EaseInQuad)
      .play();
  }
}
