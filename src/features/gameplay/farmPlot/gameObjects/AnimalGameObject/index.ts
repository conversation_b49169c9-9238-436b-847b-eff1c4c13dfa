// index.ts
import type { IAnimalGameObject } from './types'
import { useTheme } from '@/themes/useTheme'

const { themeName } = useTheme()

let GameObjectClass: IAnimalGameObject

switch (themeName.value) {
  case 'mf':
    GameObjectClass = (await import('./mf')).CowGameObject as IAnimalGameObject
    break
  case 'gb':
  default:
    GameObjectClass = (await import('./gb')).GooseGameObject as IAnimalGameObject
    break
}

export default GameObjectClass
