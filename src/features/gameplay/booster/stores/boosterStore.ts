// stores/boosterStore.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getBoosters, getActiveBoosters } from '@/features/iap'
import { devLog } from '@/utils/config'

export const useBoosterStore = defineStore('booster', () => {
  const boosters = ref([])
  const activeBoosters = ref([])

  const fetchBoosters = async () => {
    const response = await getBoosters()
    boosters.value = response.boosters
    devLog("boosters",boosters.value)
  }

  const fetchActiveBoosters = async () => {
    const response = await getActiveBoosters()
    activeBoosters.value = response.activeBoosters
    devLog("activeBoosters",activeBoosters.value)
  }

  const refreshAll = async () => {
    await fetchBoosters()
    await fetchActiveBoosters()
  }

  return {
    boosters,
    activeBoosters,
    fetchBoosters,
    fetchActiveBoosters,
    refreshAll
  }
})
