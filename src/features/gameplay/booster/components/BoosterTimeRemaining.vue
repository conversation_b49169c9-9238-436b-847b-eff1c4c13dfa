<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

interface BoosterTimeRemainingProps {
  endTime: string
}

const props = defineProps<BoosterTimeRemainingProps>()

const currentTime = ref(new Date())
let timer: ReturnType<typeof setTimeout> | null = null


const remainingTime = computed(() => {
  const endDate = new Date(props.endTime)
  const now = currentTime.value
  const diff = endDate.getTime() - now.getTime()
  
  if (diff <= 0) {
    return 'Expired'
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  // Format: "NN hr NN m NN s left"
  const parts = []
  
  if (hours > 0) {
    parts.push(`${hours} hr`)
  }
  
  if (minutes > 0 || hours > 0) {
    parts.push(`${minutes} m`)
  }
  
  parts.push(`${seconds} s`)
  parts.push('left')
  
  return parts.join(' ')
})

const isExpired = computed(() => {
  const endDate = new Date(props.endTime)
  const now = currentTime.value
  return endDate.getTime() <= now.getTime()
})

const updateTime = () => {
  currentTime.value = new Date()
}

onMounted(() => {
  // Update time immediately
  updateTime()
  
  // Set up timer to update every second
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<template>
  <div class="booster-time-remaining" :class="{ expired: isExpired }">
    {{ remainingTime }}
  </div>
</template>

<style scoped>
.booster-time-remaining {
  color: #FDD99B;
  font-size: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 4);
  text-shadow: none;
  text-align: center;
  margin-top: calc(var(--base-unit) * 4);
  font-weight: bold;
}

.booster-time-remaining.expired {
  color: #ff6b6b;
}
</style> 