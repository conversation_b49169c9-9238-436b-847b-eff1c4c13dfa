import { defineStore } from 'pinia'
import { ref } from 'vue'
import { fetchMainTask } from '@/features/newTask'
import { devLog } from '@/utils/config'


export const useMainTaskStore = defineStore('MainTaskStore', () => {
    const mainTask = ref(null)
    const loading = ref(false)
    const error = ref(null)

    const updateMainTask = async () => {
        if (loading.value) {
            devLog('updateMainTask: already refreshing, skip')
            return
        }

        loading.value = true
        error.value = null

        try {
            const result = await fetchMainTask()
            mainTask.value = result.mainTask
            devLog('updateMainTask:', mainTask.value)
        } catch (err) {
            console.error('fetchMainTask error:', err)
            error.value = err.message
            mainTask.value = null
        } finally {
            loading.value = false
        }
    }

    return {
        mainTask,
        loading,
        error,
        updateMainTask,
    }
})
