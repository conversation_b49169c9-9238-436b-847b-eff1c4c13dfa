<template>
    <div class="task-bar" @click="handleClick">
        <img src="/ui/task.png" class="task-bar-icon">
        <div class="task-bar-content">
            <Transition>
                <div class="task-bar-describe" :key="mainTask?.id" v-show="mainTask">
                    <div>{{ mainTask?.progressText }}</div>
                    <div>{{ mainTask?.taskConfig?.describe }}</div>
                    <img src="/ui/task-check.png" class="task-bar-check"
                        :class="{ completed: !mainTask?.isCompleted }" />
                </div>
            </Transition>
            <img src="/ui/task-arrow.png" class="task-bar-arrow">
        </div>
    </div>
</template>

<script setup lang="ts">
import audioService from '@/lib/audioService'

import { useNewTaskPopupStore } from '@/features/newTask'
import { useMainTaskStore } from '@/features/newTask'
import { storeToRefs } from 'pinia'

const { openNewTaskPopup } = useNewTaskPopupStore()
const mainTaskStore = useMainTaskStore()
const { mainTask } = storeToRefs(mainTaskStore)

function handleClick() {
    audioService.play('button1')
    openNewTaskPopup()
}
</script>

<style scoped>
.task-bar {
    display: flex;
    align-items: center;
    margin: 0 calc(var(--base-unit) * 16);
    margin-bottom: calc(var(--base-unit) * 24);
    user-select: none;
}

.task-bar .task-bar-icon {
    flex-shrink: 0;
    width: calc(var(--base-unit) * 56);
    width: calc(var(--base-unit) * 56);
    position: relative;
    transform: translate(calc(var(--base-unit) * -7), calc(var(--base-unit) * -2));
}

.task-bar-content {
    display: flex;
    align-items: center;
    height: calc(var(--base-unit) * 36);
    width: 100%;
    background-color: #D29264;
    color: #FFEFBD;
    border-radius: calc(var(--base-unit) * 12);
    border: calc(var(--base-unit) * 1) solid #FDD99B;
    box-sizing: border-box;
    margin-left: calc(var(--base-unit) * -30);
    padding-left: calc(var(--base-unit) * 20);
    padding-right: calc(var(--base-unit) * 7);
    transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.task-bar-check {
    flex-shrink: 0;
    width: calc(var(--base-unit) * 24);
    overflow: hidden;
}

.task-bar-describe {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: calc(var(--base-unit) * 3);
    font-size: calc(var(--base-unit) * 14);
    flex-shrink: 0;
}

.task-bar-arrow {
    width: calc(var(--base-unit) * 24);
    margin-left: auto;
}

.completed {
    filter: grayscale(100%);
}

/* Press effect */
.task-bar:active {
    transform: scale(0.95);
    /* Slightly shrink button */
    box-shadow: inset 0 0 calc(var(--base-unit) * 82) rgba(0, 0, 0, 0.2);
    /* Add inset shadow */
}

.v-enter-active,
.v-leave-active {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
    transform: translateX(-10px);
}
</style>