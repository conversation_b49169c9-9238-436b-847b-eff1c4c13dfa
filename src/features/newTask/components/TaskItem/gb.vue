<template>
    <div class="task-item">
        <div class="task-item-title">
            {{ task.taskConfig.describe }}
        </div>

        <div class="progress-wrapper" :style="{ '--progress': `${task?.progressPercentage ?? 0}%` }">
            <span class="progress-text">{{ task.progressText }}</span>
            <div class="progress-value ">
            </div>
        </div>

        <div class="task-item-footer">
            <div class="task-reward">
                <RewardItem :id="reward.id" v-for="reward in task.taskConfig.rewards" :type="reward.type" :amount="reward.amount"
                    :disabled="!task.canClaim" />
            </div>

            <ColorButton class="button claim-button" :class="{ 'disabled-button': !task.canClaim || task.isClaimed }"
                @click="$emit('claim')">
                {{ task.isClaimed ? t('newTask.claimed') : t('common.claim') }}
            </ColorButton>
        </div>
    </div>
</template>

<script setup>
import { ColorButton, RewardItem } from '@/components/common'
import { useI18n } from 'vue-i18n';

defineProps({
    task: {
        type: Object,
        required: true
    }
})

defineEmits(['claim'])

const { t } = useI18n();
</script>

<style scoped>
.task-item {
    background-color: #D4B7A7;
    padding: calc(var(--base-unit) * 10);
    border-radius: calc(var(--base-unit) * 12);
    display: grid;
    gap: calc(var(--base-unit) * 8);
    user-select: none;
}

.task-item-title {
    font-size: calc(var(--base-unit) * 14);
    height: calc(var(--base-unit) * 18);
    color: #68514B;
    font-weight: 600;
}

.progress-wrapper {
    --h: calc(var(--base-unit) * 20);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: calc(var(--base-unit) * 10);
    height: var(--h);
    background: #C8A28E;
    border-radius: calc(var(--base-unit) * 5);
    border: calc(var(--base-unit) * 2) solid #E9EAEB;
    position: relative;
    overflow: hidden;
}

.progress-text {
    z-index: 1;
}

.progress-value {
    border-radius: calc(var(--base-unit) * 4);
    position: absolute;
    background: #FFAA18;
    width: var(--progress);
    height: 100%;
    z-index: 0;
    left: 0;
}

.task-item-footer .button {
    flex-shrink: 0;
}

.task-item-footer {
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: calc(var(--base-unit) * 8);
}

.task-reward {
    overflow-x: auto;
    overflow-y: hidden;
    display: flex;
    width: 100%;
    gap: calc(var(--base-unit) * 8);
}

.disabled-button {
    filter: grayscale(100%);
    cursor: not-allowed;
}
</style>