<template>
    <AppPopup :isOpen="isNewTaskShow" @close="closeNewTaskPopup()">
        <template #title>
            <TitlePanelColor>{{ t('newTask.title') }}</TitlePanelColor>
        </template>

        <div class="task-list">
            <TransitionGroup name="fade">
                <TaskItem v-for="task in tasks" :key="task.id" :task="task" @claim="() => handleClaimClick(task)"
                    v-show="!task.isClaimed" />
            </TransitionGroup>
        </div>
    </AppPopup>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { TaskItem, useTaskBarPopup } from '@/features/newTask'
import { TitlePanelColor, AppPopup } from '@/components/common'

const { t } = useI18n()
const { tasks, isNewTaskShow, handleClaimClick, closeNewTaskPopup } = useTaskBarPopup()
</script>

<style scoped src="./style.css"></style>