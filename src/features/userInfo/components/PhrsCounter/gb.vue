<script setup>
import { onMounted, computed } from 'vue'
import { useUserInfo } from '@/features/userInfo'
import { useTopupPopupStore } from '@/features/topup'

const { userInfo, fetchUserInfo } = useUserInfo()
const topupPopupStore = useTopupPopupStore()

const openTopupPopup = () => {
  topupPopupStore.openTopupPopup()
}

const formatBalance = (balance) => {
  if (!balance) return '0.00'
  
  const num = Number(balance)
  if (isNaN(num)) return '0.00'
  
  if (num < 0.01) {
    return num.toFixed(8).replace(/\.?0+$/, '') || '0.00'
  } else if (num < 1) {
    return num.toFixed(4).replace(/\.?0+$/, '') || '0.00'
  } else {
    return num.toFixed(2)
  }
}

const formattedBalance = computed(() => {
  return formatBalance(userInfo.value?.phrsBalance)
})

onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div class="panel-container">
    <img class="phrs-icon" src="/icon/gb-phrs.png" alt="phrs" />
    <div class="panel">
      <div class="pattern-overlay"></div>
      <div class="content-wrapper">
        {{ formattedBalance }}
      </div>
      <button class="add-button" @click="openTopupPopup">
        <img src="/icon/plus.png" alt="add" />
      </button>
    </div>
  </div>
</template>

<style scoped>
.phrs-icon {
  position: absolute;
  top: calc(var(--base-unit) * 0);
  left: calc(var(--base-unit) * 190);
  width: calc(var(--base-unit) * 50);
  height: calc(var(--base-unit) * 60);
  object-fit: contain;
  z-index: 2;
}

.panel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: calc(var(--base-unit) * 8);
}

.panel {
  position: relative;
  width: calc(var(--base-unit) * 160);
  left: calc(var(--base-unit) * 8);
  height: calc(var(--base-unit) * 30);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  background: rgba(5, 76, 187, 1);
  box-sizing: border-box;
  border-radius: calc(var(--base-unit) * 8);
  border: calc(var(--base-unit) * 2) solid rgba(244, 140, 7, 1);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pattern-overlay {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  /* background: url('/ui/wooden-texture.png') center center; */
  background-size: cover;
  pointer-events: none;
}

.content-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: skew(0deg);
  width: 100%;
  color: rgba(255, 255, 255, 1);
  -webkit-text-stroke: 2px rgba(37, 43, 55, 1);
  font-size: calc(var(--base-unit) * 18);
}

.add-button {
  position: absolute;
  right: calc(var(--base-unit) * 8);
  top: 50%;
  transform: translateY(-50%);
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  background: none;
  border: none;
  cursor: pointer;
  z-index: 3;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.2s ease;
}

.add-button:hover {
  transform: translateY(-50%) scale(1.1);
}

.add-button:active {
  transform: translateY(-50%) scale(0.9);
}

.add-button img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style> 