<script setup>
import { onMounted } from 'vue'
import { useUserInfo } from '@/features/userInfo'

import { usePlayerPopupStore } from '../../stores/playerPopupStore'
import audioService from '@/lib/audioService'

const playerPopupStore = usePlayerPopupStore()
const { userInfo, fetchUserInfo } = useUserInfo()

const OpenPopup = () => {
  audioService.play('button1'); // Play sound effect
  playerPopupStore.openPlayerPopup()
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div class="nickname-container" @click="OpenPopup">
    <div class="icon-box">
      <img :src="userInfo?.photoUrl || '/icon/profile-icon.png'" alt="Profile" class="profile-icon" />
    </div>
    <div class="name-banner">
      {{ userInfo?.username ?? '' }}
    </div>
  </div>
</template>

<style scoped>
.nickname-container {
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 10);
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 14);
  background-image: linear-gradient(to bottom, rgba(255, 253, 255, 1), rgba(211, 221, 253, 1));
  border-radius: calc(var(--base-unit) * 8) calc(var(--base-unit) * 8) calc(var(--base-unit) * 16) calc(var(--base-unit) * 8);
  width: calc(var(--base-unit) * 119);
  height: calc(var(--base-unit) * 40);
  box-sizing: border-box;
}

.nickname-container:active {
  transform: scale(0.98);
}

.icon-box {
  width: calc(var(--base-unit) * 32);
  height: calc(var(--base-unit) * 32);
  border-radius: calc(var(--base-unit) * 6);
  border: calc(var(--base-unit) * 1) solid rgba(0, 0, 0, 1);
  overflow: hidden;
  flex-shrink: 0;
}

.profile-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.name-banner {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: calc(var(--base-unit) * 14);
  color: rgba(0, 0, 19, 1);
  -webkit-text-stroke: 0;
  text-shadow: none;
  min-width: 0;
}
</style>
