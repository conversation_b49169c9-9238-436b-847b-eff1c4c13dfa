// features/userInfo/composables/useUserInfo.js
import { ref } from 'vue'
import { getUserInfo } from '../api'
import { devLog } from '@/utils/config'

const userInfo = ref(null)

export const useUserInfo = () => {
  const fetchUserInfo = async () => {
    const { data } = await getUserInfo()
    devLog("getUserInfo:", data);
    if (!userInfo.value) userInfo.value = {}
    userInfo.value = { ...(Array.isArray(data) && data.length > 0 ? data[0] : {}) }


  }

  return {
    userInfo,
    fetchUserInfo,
  }
}
