import { useThemeAssets } from '@/themes'

export function useChestAssets() {
  const currentTheme = useThemeAssets()
  const theme = currentTheme.value

  const chestImages = {
    closed: '/icon/chest-closed.png',
    opened: '/icon/chest-opened.png'
  }

  const itemImages = {
    gem: theme.gem,
    ticket: '/icon/ticket.png',
    ton: '/icon/toncoin.webp',
    fragment_green: '/icon/green-fragment.png',
    fragment_blue: '/icon/blue-fragment.png',
    fragment_purple: '/icon/purple-fragment.png',
    fragment_gold: '/icon/gold-fragment.png'
  }

  const getItemImage = (type) => itemImages[type] || '/icon/wallet.png'

  const formatItemName = (type, amount) => {
    const names = {
      gem: amount > 1 ? 'Gems' : 'Gem',
      ticket: amount > 1 ? 'Tickets' : 'Ticket',
      ton: 'TON',
      fragment_green: amount > 1 ? 'Green Fragments' : 'Green Fragment',
      fragment_blue: amount > 1 ? 'Blue Fragments' : 'Blue Fragment',
      fragment_purple: amount > 1 ? 'Purple Fragments' : 'Purple Fragment',
      fragment_gold: amount > 1 ? 'Gold Fragments' : 'Gold Fragment'
    }
    return `${amount} ${names[type] || type}`
  }

  return {
    chestImages,
    getItemImage,
    formatItemName
  }
}
