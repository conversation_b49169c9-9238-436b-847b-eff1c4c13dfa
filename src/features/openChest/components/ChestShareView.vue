<script setup lang="ts">
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useChestOverlayStore } from '../stores/chestOverlayStore'
import { generateBoostLink } from '@/utils/config'

const chestOverlay = useChestOverlayStore()
const { t } = useI18n()

const emit = defineEmits<{
  (e: 'next'): void
}>()

const fullLink = chestOverlay.currentChestConfig.shareLink
  ? generateBoostLink(chestOverlay.currentChestConfig.shareLink)
  : ''

const copyShareLink = () => {
  if (navigator.clipboard && chestOverlay.currentChestConfig.shareLink) {
    navigator.clipboard.writeText(fullLink)
      .then(() => console.log('Copied:', fullLink))
      .catch(err => console.error('Failed to copy:', err))
  }
}

onMounted(() => {
  if (!chestOverlay.currentChestConfig.shareLink) emit('next')
})
</script>


<template>
  <div class="share-container" @click.stop>
    <h2 class="oc-heading">{{ t('chest.shareRewards') }}</h2>
    <div class="share-link-container">
      <input type="text" readonly :value="fullLink" class="share-input" />
      <button class="oc-button" @click="copyShareLink">{{ t('button.copy') }}</button>
    </div>
    <button class="oc-button" @click="emit('next')">{{ t('button.next') }}</button>
  </div>
</template>


<!-- Import shared styles globally within this component's scope -->
<style src="@/features/openChest/styles/shared.css"></style>

<!-- Scoped styles for this component only -->
<style scoped>
.share-container {
  width: 100%;
  text-align: center;
  animation: slideIn 0.5s ease-out;
  margin: 0 calc(var(--base-unit) * 20);
  color: #ffd700;
}

.share-link-container {
  display: flex;
  margin: calc(var(--base-unit) * 24) 0;
  width: 100%;
  gap: calc(var(--base-unit) * 8);
}

.share-input {
  flex-grow: 1;
  padding: calc(var(--base-unit) * 5);
  border-radius: calc(var(--base-unit) * 4);
  border: calc(var(--base-unit) * 1) solid #444;
  background-color: #222;
  color: white;
}

@keyframes slideIn {
  from { transform: translateY(calc(var(--base-unit) * 30)); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
</style>
