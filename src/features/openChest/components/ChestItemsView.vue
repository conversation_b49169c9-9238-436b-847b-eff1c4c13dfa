<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useChestOverlayStore } from '../stores/chestOverlayStore'
import ResourceCard from './ResourceCard.vue'

const { t } = useI18n()
const chestOverlay = useChestOverlayStore()

const emit = defineEmits(['next'])
</script>


<template>
  <div class="items-container" @click.stop>
    <h2 class="oc-heading">{{ t('chest.rewards') }}</h2>
    <div class="cards-grid">
      <div v-for="(item, index) in chestOverlay.currentChestConfig.items" :key="index">
        <ResourceCard
          :title="item.name"
          :imageSrc="item.image"
          :contentShadow="null"
          :iconBorderWidth="1"
          :contentDecorShow="true"
          bgColor="#BF7E58"
          borderColor="#572918"
          contentBg="#BF7E58"
          iconBg="linear-gradient(0deg, #8DCBDF 0%, #738EDB 100%)"
          iconBorderColor="#FFF"
        />
      </div>
    </div>
    <button class="oc-button" @click="emit('next')">{{ t('button.next') }}</button>
  </div>
</template>


<!-- Import shared styles globally within this component's scope -->
<style src="@/features/openChest/styles/shared.css"></style>

<!-- Scoped styles for this component only -->
<style scoped>
.items-container {
  width: 100%;
  animation: slideIn 0.5s ease-out;
  padding: 0 calc(var(--base-unit) * 20);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #ffd700;
}

.cards-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: calc(var(--base-unit) * 8);
  margin: calc(var(--base-unit) * 2) 0;
  max-width: 100%;

}

.cards-grid > div {
  width: calc(var(--base-unit) * 92);
  margin-bottom: calc(var(--base-unit) * 8);
}

.next-button {
  background-color: #ffd700;
  color: #000;
  border: none;
  border-radius: calc(var(--base-unit) * 6);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.next-button:hover {
  transform: scale(1.05);
  box-shadow: 0 0 calc(var(--base-unit) * 10) rgba(255, 215, 0, 0.7);
}

@keyframes slideIn {
  from { transform: translateY(calc(var(--base-unit) * 30)); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
</style>