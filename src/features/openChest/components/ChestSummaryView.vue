<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useChestOverlayStore } from '../stores/chestOverlayStore'
import { useChestAssets } from '../composables/useChestAssets'
import ResourceCard from './ResourceCard.vue'

const chestOverlay = useChestOverlayStore()
const { formatItemName, getItemImage } = useChestAssets()
const { t } = useI18n()

defineEmits(['close'])
</script>


<template>
  <div class="summary-container" @click.stop>
    <h2 class="oc-heading">{{ t('chest.totalRewards') }}</h2>
    <div class="cards-grid">
      <div
        v-for="[type, amount] in Object.entries(chestOverlay.summary).filter(([_, v]) => Number(v) > 0)"
        :key="type"
      >
        <ResourceCard
          :title="formatItemName(type, amount)"
          :imageSrc="getItemImage(type)"
          :contentShadow="null"
          :iconBorderWidth="1"
          :contentDecorShow="true"
          bgColor="#BF7E58"
          borderColor="#572918"
          contentBg="#BF7E58"
          iconBg="linear-gradient(0deg, #8DCBDF 0%, #738EDB 100%)"
          iconBorderColor="#FFF"
        />
      </div>
    </div>
    <div class="chest-buttons">
      <button class="oc-button" @click="$emit('close')">{{ t('general.close') }}</button>
    </div>
  </div>
</template>


<!-- Import shared styles globally within this component's scope -->
<style src="@/features/openChest/styles/shared.css"></style>

<!-- Scoped styles for this component only -->
<style scoped>
.summary-container {
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-top: 2rem;
}

.cards-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: calc(var(--base-unit) * 8);
  margin-top: 1rem;
}

.cards-grid > div {
  width: calc(var(--base-unit) * 92);
  margin-bottom: calc(var(--base-unit) * 8);
}

.chest-buttons {
  display: flex;
  margin-top: 1rem;
  justify-content: center;
}
</style>