import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getStoreProducts, createPayment, purchaseWithPhrs } from '../api'
import { useNotificationStore } from '@/features/notification'
import { useDappPortalSdk } from '@/features/auth'
import { useI18n } from 'vue-i18n'

export const useShopStore = defineStore('shop', () => {
  const { startPayment } = useDappPortalSdk()
  const { t } = useI18n()

  const products = ref([])
  const processing = ref(false)
  const paymentStep = ref('')

  const fetchProducts = async () => {
    try {
      const response = await getStoreProducts()
      products.value = response.products || []
      return products.value
    } catch (error) {
      const notificationStore = useNotificationStore()
      notificationStore.addNotification({
        type: 'error',
        message: 'Failed to load shop products'
      })
      throw error
    }
  }

  const resetStore = () => {
    products.value = []
    processing.value = false
    paymentStep.value = ''
  }

  const purchaseWithPhrsBalance = async (productId) => {
    if (processing.value) return
    processing.value = true
    paymentStep.value = 'creating'
    
    try {
      const response = await purchaseWithPhrs({ productId })
      
      paymentStep.value = 'completed'
      
      const notificationStore = useNotificationStore()
      notificationStore.addNotification({
        type: 'success',
        message: response.message || t('shop.payment.success')
      })
      
      return response
    } catch (error) {
      paymentStep.value = 'failed'
      
      const notificationStore = useNotificationStore()
      
      let errorMessage
      
      if (error.response?.data?.error) {
        const errorCode = error.response.data.error
        const errorData = error.response.data
        
        switch (errorCode) {
          case 'PURCHASE_LIMIT_EXCEEDED':
            errorMessage = t('shop.payment.errors.purchaseLimitExceeded')
            break
          case 'DAILY_LIMIT_EXCEEDED':
            errorMessage = t('shop.payment.errors.dailyLimitExceeded')
            break
          case 'ACCOUNT_LIMIT_EXCEEDED':
            errorMessage = t('shop.payment.errors.accountLimitExceeded')
            break
          case 'INSUFFICIENT_BALANCE':
            errorMessage = t('shop.payment.errors.insufficientBalance')
            break
          case 'PRODUCT_NOT_AVAILABLE':
            errorMessage = t('shop.payment.errors.productNotAvailable')
            break
          case 'VIP_ALREADY_ACTIVE':
            errorMessage = t('shop.payment.errors.vipAlreadyActive')
            break
          default:
            errorMessage = errorData.message || t('shop.payment.errors.generic')
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      } else {
        errorMessage = t('shop.payment.errors.generic')
      }
      
      notificationStore.addNotification({
        type: 'error',
        message: errorMessage
      })
      throw error
    } finally {
      processing.value = false
      setTimeout(() => {
        paymentStep.value = ''
      }, 2000)
    }
  }

  const purchaseProduct = async (paymentData) => {
    if (processing.value) return
    processing.value = true
    paymentStep.value = 'creating'
    
    try {
      const response = await createPayment(paymentData)
      const paymentId = response?.paymentId
      
      if (!paymentId) {
        throw new Error('No payment ID received from server')
      }

      paymentStep.value = 'starting'
      
      const paymentResult = await startPayment(paymentId)
      
      paymentStep.value = 'completed'
      
      const notificationStore = useNotificationStore()
      notificationStore.addNotification({
        type: 'success',
        message: t('shop.payment.success')
      })
      
      return { ...response, paymentResult }
    } catch (error) {
      paymentStep.value = 'failed'
      
      const notificationStore = useNotificationStore()
      
      let errorMessage
      if (error.code === -32000 && error.message === 'insufficient funds for transfer') {
        errorMessage = t('shop.payment.errors.insufficientFunds')
      } else if (error.code === -31001) {
        errorMessage = t('shop.payment.errors.canceled')
      } else if (error.code === -31002) {
        errorMessage = t('shop.payment.errors.failed')
      } else {
        errorMessage = error.message || t('shop.payment.errors.generic')
      }
      
      notificationStore.addNotification({
        type: 'error',
        message: errorMessage
      })
      throw error
    } finally {
      processing.value = false
      setTimeout(() => {
        paymentStep.value = ''
      }, 2000)
    }
  }

  const initializeShop = async () => {
    try {
      await fetchProducts()
    } catch (error) {
      console.error('Failed to initialize shop:', error)
    }
  }

  const cancelPayment = () => {
    processing.value = false
    paymentStep.value = ''
    
    const notificationStore = useNotificationStore()
    notificationStore.addNotification({
      type: 'info',
      message: t('shop.payment.canceled')
    })
  }

  return {
    // State
    products,
    processing,
    paymentStep,

    // Actions
    fetchProducts,
    purchaseProduct,
    purchaseWithPhrsBalance,
    resetStore,
    initializeShop,
    cancelPayment
  }
}) 