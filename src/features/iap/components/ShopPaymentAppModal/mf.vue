<template>
    <div :class="['info-panel']">
        <slot></slot>
    </div>
</template>

<style scoped>

.info-panel {
    padding: calc(var(--base-unit) * 14) calc(var(--base-unit) * 14);
    padding-bottom: calc(var(--base-unit) * 24);
    box-shadow: inset 0 0 0 calc(var(--base-unit) * 4) #FDD99B;
    border-radius: calc(var(--base-unit) * 24);
    background: #CF8E61;
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
    color: rgba(75, 89, 116, 1);
    -webkit-text-stroke: 0;
    text-shadow: none;
}


.bottom {
    display: flex;
    justify-content: center;
    align-items: center;
}

.bottom img {
    height: calc(var(--base-unit) * 28);
}

.bottom.white  {
    display: hidden;
}
</style>