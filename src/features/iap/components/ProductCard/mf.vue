<script setup>
defineProps({
  productId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: [Number, String],
    required: true
  },
  variant: {
    type: String,
    default: 'default'
  },
  canPurchase: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click')
}
</script>

<template>
  <div 
    :class="['product-card', 'time-wrap', variant, { 'disabled': !canPurchase }]"
    @click="handleClick"
  >
    <div class="product-icon">
      <img :src="`/iap/${productId}.png`" alt="product icon" />
    </div>
    <div class="product-label">{{ name }}</div>
    <div class="product-price">
      <img src="/iap/kaia.png" alt="kaia" />
      <span>{{ price }}</span>
    </div>
  </div>
</template>

<style scoped>
* {
  font-family: 'Urbanist';
}

.product-card.time-wrap {
  background-color: #CF8E61;
  border-radius: calc(var(--base-unit) * 10);
  padding: calc(var(--base-unit) * 10);
  text-align: center;
  box-shadow: none;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(var(--base-unit) * 130);
  position: relative;
  border: calc(var(--base-unit) * 4) solid #FDD99B;
  margin-bottom: calc(var(--base-unit) * 30);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.product-card.time-wrap.gold {
  background: linear-gradient(to bottom, #F59A13, #A74B8D);
}

.product-card:active {
  transform: scale(0.97);
}

.product-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card.disabled:active {
  transform: none;
}

.product-icon {
  margin-bottom: calc(var(--base-unit) * 4);
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(var(--base-unit) * 60);
  flex-shrink: 0;
}

.product-icon img {
  width: calc(var(--base-unit) * 60);
  height: calc(var(--base-unit) * 60);
  object-fit: contain;
  max-width: none;
}

.product-card.time-wrap .product-label {
  font-size: calc(var(--base-unit) * 12);
  font-weight: 700;
  color: #FCF1CB;
  margin-bottom: calc(var(--base-unit) * 4);
  line-height: 1.3;
  height: calc(var(--base-unit) * 32);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.product-card.time-wrap .product-price {
  background: #FDC844;
  color: white;
  font-weight: bold;
  padding: calc(var(--base-unit) * 2) calc(var(--base-unit) * 8);
  border-radius: calc(var(--base-unit) * 10);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 6);
  border: calc(var(--base-unit) * 3) solid #FF8A28;
  box-shadow: 0 0 0 calc(var(--base-unit) * 2) black;
  width: 70%;
  margin-top: auto;
  position: absolute;
  bottom: calc(var(--base-unit) * -20);
  left: 50%;
  transform: translateX(-50%);
}

.product-card.time-wrap .product-price img {
  width: calc(var(--base-unit) * 20);
  height: calc(var(--base-unit) * 20);
  object-fit: contain;
  flex-shrink: 0;
  display: block;
  max-width: none;
}

.product-card.time-wrap .product-price span {
  font-size: calc(var(--base-unit) * 12);
  -webkit-text-stroke-width: calc(var(--base-unit) * 2);
  -webkit-text-stroke-color: #3B3B3B;
}
</style> 