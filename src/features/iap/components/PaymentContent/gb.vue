<script setup>
import { ref } from 'vue'
import { useThemeAssets } from '@/themes'

const theme = useThemeAssets()

// 格式化价格显示，去掉末尾的0
const formatPrice = (priceStr) => {
  if (!priceStr) return '0'
  
  const price = parseFloat(priceStr)
  if (isNaN(price)) return '0'
  
  // 转换为字符串并去掉末尾的0
  return price.toString().replace(/\.?0+$/, '')
}
  
const props = defineProps({
  phrsPrice: {
    type: [Number, String],
    required: true
  },
  processing: {
    type: Boolean,
    default: false
  },
  paymentStep: {
    type: String,
    default: 'initial'
  }
})

const emit = defineEmits(['select', 'close'])

const handleConfirmPurchase = () => {
  if (props.processing) return
  emit('select', 'phrs')
}
</script>

<template>
  <div class="payment-content">    
    <div class="payment-order-content">
      <div class="payment-header">
        <div class="payment-title">Booster</div>
        <div class="close-button" @click="$emit('close')">
          <img :src="theme.closeBtn" alt="Close" class="close-button-image" />
        </div>
      </div>
      
      <div class="line-divider"></div>

      <div class="payment-options">
        <div 
          :class="['payment-option', 'phrs-option', { 'processing': processing }]" 
          @click="handleConfirmPurchase">
          <div v-if="processing" class="loading-spinner"></div>
          <span>{{ formatPrice(phrsPrice) }} PHRS</span>
        </div>
      </div>

      <div class="line-divider"></div>
      
      <div class="payment-terms">
        <div class="term-item">1. You agree that the product(s)is/are non-refundable</div>
        <div class="term-item">2. If paid via LINE IAP , you agree to providing encrypted ID info to LY Corporation.</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
* {
  font-family: 'Urbanist';
}

.payment-order-content {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 10);
}

.line-divider {
  width: 100%;
  height: 2px;
  background: #E9EAEB;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-title {
  font-size: calc(var(--base-unit) * 20);
  font-weight: bold;
  color: #68514B;
  text-align: left;
}

.close-button {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: calc(var(--base-unit) * 40);
  height: calc(var(--base-unit) * 40);
}

.close-button-image {
  width: calc(var(--base-unit) * 36);
  height: calc(var(--base-unit) * 36);
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
}

.payment-option {
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 16) calc(var(--base-unit) * 20);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: white;
  gap: calc(var(--base-unit) * 8);
}

.payment-option:hover {
  transform: scale(1.02);
}

.payment-option:active {
  transform: scale(0.98);
}

.payment-option.processing {
  cursor: not-allowed;
  opacity: 0.8;
}

.payment-option.processing:hover {
  transform: none;
}

.payment-option.processing:active {
  transform: none;
}

.phrs-option {
  background: rgba(192, 150, 250, 1);
  border: 1px solid rgba(97, 114, 243, 1);
  box-shadow: 
    0 2px 8px rgba(155, 89, 182, 0.3),
    inset 0 0 0 1px rgba(10, 13, 18, 0.18),
    inset 0 -2px 0 0 rgba(10, 13, 18, 0.05);
}

.payment-option.phrs-option span {
  font-size: calc(var(--base-unit) * 16);
  color: rgba(53, 56, 205, 1);
}

.loading-spinner {
  width: calc(var(--base-unit) * 16);
  height: calc(var(--base-unit) * 16);
  border: calc(var(--base-unit) * 2) solid rgba(53, 56, 205, 0.3);
  border-top: calc(var(--base-unit) * 2) solid rgba(53, 56, 205, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.payment-terms {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 8);
  text-align: left;
}

.term-item {
  font-size: calc(var(--base-unit) * 12);
  color: rgba(113, 118, 128, 1);
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 