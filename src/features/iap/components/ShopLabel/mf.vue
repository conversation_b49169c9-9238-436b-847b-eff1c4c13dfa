<template>
    <div class="banner-wrapper">
      <div class="banner-container">
        <div class="banner-content">
          {{ text }}
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ShopLabel',
    props: {
      text: {
        type: String,
        required: true,
        default: 'Category'
      }
    }
  }
  </script>
  
<style scoped>
.banner-wrapper {
  position: relative;
  height: calc(var(--base-unit) * 46);
  margin-top: calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 10);
}

.banner-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 80%;
  z-index: 2;
  border-radius:
  calc(var(--base-unit) * 16)
  calc(var(--base-unit) * 16)
  0
  0;
  background-color: #A55F3B;
  border: calc(var(--base-unit) * 3) solid #FDD99B;
}

.banner-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 1);
  font-size: calc(var(--base-unit) * 20);
  text-shadow: none;
  -webkit-text-stroke: calc(var(--base-unit) * 3) #251B12;
}
  </style>
  