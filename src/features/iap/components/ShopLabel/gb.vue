<script>
  export default {
    name: '<PERSON><PERSON>abel',
    props: {
      text: {
        type: String,
        required: true,
        default: 'Category'
      }
    }
  }
</script>
  
<template>
    <div class="banner-wrapper">
      <div class="banner-container">
        <div class="banner-content">
          {{ text }}
        </div>
      </div>
    </div>
</template>
<style scoped>
.banner-wrapper {
  position: relative;
  height: calc(var(--base-unit) * 46);
  margin-top: calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 10);
}

.banner-container {
  background-image: url('/iap/iap-label-bg.png');
  background-size: 80%;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  height: 80%;
  z-index: 2;
  border-radius:
  calc(var(--base-unit) * 12)
  calc(var(--base-unit) * 12)
  calc(var(--base-unit) * 6)
  calc(var(--base-unit) * 6);
  background-color: rgba(192, 124, 90, 1);
  border: 3px solid rgba(166, 100, 52, 1);
  box-shadow: 0 4px 0px -2px rgba(0, 0, 0, 0.18);
}

.banner-content {
  
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: rgba(255, 239, 189, 1);
  font-size: calc(var(--base-unit) * 20);
  text-shadow: none;
  -webkit-text-stroke: calc(var(--base-unit) * 3) rgba(127, 82, 26, 1);
}
  </style>
  