import { computed } from 'vue'
import { useShopStore } from '../stores/shopStore'
import { useTheme } from '@/themes/useTheme'

const formatPrice = (priceStr) => {
  if (!priceStr) return '0'
  
  const price = parseFloat(priceStr)
  if (isNaN(price)) return '0'
  
  return price.toString().replace(/\.?0+$/, '')
}

export function useProductData() {
  const shopStore = useShopStore()
  const { themeName } = useTheme()

  const specialOfferProducts = computed(() => {
    return shopStore.products
      .filter(product => product.type === 'special_offer')
      .map(product => {
        const price = theme === 'gb' ? product.pricePhrs : product.priceKaia
        const formattedPrice = theme === 'gb' ? formatPrice(product.pricePhrs) : formatPrice(product.priceKaia)
        
        return {
          id: product.productId,
          createdOrderID: product.id,
          name: product.name,
          price: formattedPrice,
          pricePhrs: product.pricePhrs || '0.00000000',
          priceKaia: product.priceKaia || '0.00000000',
          priceUsd: product.priceUsd || '0.00',
          category: 'specialOffer',
          variant: 'default',
          canPurchase: product.canPurchase !== false,
          reason: product.reason || '',
          description: product.description || '',
          originalProduct: product
        }
      })
  })

  const timeWrapProducts = computed(() => {
    return shopStore.products
      .filter(product => product.type === 'time_warp')
      .map(product => {
        const price = theme === 'gb' ? product.pricePhrs : product.priceKaia
        const formattedPrice = theme === 'gb' ? formatPrice(product.pricePhrs) : formatPrice(product.priceKaia)
        
        return {
          id: product.productId,
          createdOrderID: product.id,
          name: product.name,
          price: formattedPrice,
          pricePhrs: product.pricePhrs || '0.00000000',
          priceKaia: product.priceKaia || '0.00000000',
          priceUsd: product.priceUsd || '0.00',
          category: 'timeWrap',
          variant: 'default',
          canPurchase: product.canPurchase !== false,
          reason: product.reason || '',
          description: product.description || '',
          originalProduct: product
        }
      })
  })

  const boosterProducts = computed(() => {
    return shopStore.products
      .filter(product => product.type === 'speed_boost')
      .map(product => {
        const multiplier = product.multiplier || 2
        const price = theme === 'gb' ? product.pricePhrs : product.priceKaia
        const formattedPrice = theme === 'gb' ? formatPrice(product.pricePhrs) : formatPrice(product.priceKaia)
        
        return {
          id: product.productId,
          createdOrderID: product.id,
          name: product.name,
          multiplier: `X${multiplier}`,
          price: formattedPrice,
          pricePhrs: product.pricePhrs || '0.00000000',
          priceKaia: product.priceKaia || '0.00000000',
          priceUsd: product.priceUsd || '0.00',
          category: 'booster',
          variant: 'default',
          canPurchase: product.canPurchase !== false,
          reason: product.reason || '',
          description: product.description || '',
          originalProduct: product
        }
      })
  })

  return {
    specialOfferProducts,
    timeWrapProducts,
    boosterProducts
  }
} 