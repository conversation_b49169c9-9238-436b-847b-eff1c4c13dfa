import { ref } from 'vue'
import { useApp<PERSON>itProvider, useAppKitNetwork } from '@reown/appkit/vue'
import { defaultNetwork } from '@/config/networks'
import { ethers } from 'ethers'
import { useI18n } from 'vue-i18n'

const CONTRACT_ADDRESS = '******************************************'
const CONTRACT_ABI = [
  {
    "inputs": [],
    "name": "deposit",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getContractInfo",
    "outputs": [
      { "internalType": "uint256", "name": "minAmount", "type": "uint256" },
      { "internalType": "uint256", "name": "maxAmount", "type": "uint256" },
      { "internalType": "uint256", "name": "contractBalance", "type": "uint256" }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{ "internalType": "address", "name": "user", "type": "address" }],
    "name": "getUserBalance",
    "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "paused",
    "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "anonymous": false,
    "inputs": [
      { "indexed": true, "internalType": "address", "name": "user", "type": "address" },
      { "indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256" },
      { "indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256" }
    ],
    "name": "Deposit",
    "type": "event"
  }
]

export const useDeposit = () => {
  const { walletProvider } = useAppKitProvider("eip155")
  const { chainId } = useAppKitNetwork(defaultNetwork)
  const { t } = useI18n()
  const isProcessing = ref(false)
  const error = ref(null)

  const getContractInfo = async () => {
    try {
      const provider = new ethers.providers.Web3Provider(walletProvider, chainId)
      const contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, provider)
      const signer = provider.getSigner()

      const [minAmount, maxAmount, contractBalance] = await contract.getContractInfo()
      const isPaused = await contract.paused()
      
      const userAddress = await signer.getAddress()
      const walletBalance = await provider.getBalance(userAddress)

      return {
        minDepositAmount: ethers.utils.formatEther(minAmount),
        maxDepositAmount: ethers.utils.formatEther(maxAmount),
        walletBalance: ethers.utils.formatEther(walletBalance),
        userAddress: userAddress,
        isPaused
      }
    } catch (error) {
      throw new Error(t('topup.failedToLoadContractInfo'))
    }
  }

  const parseDepositEvent = (receipt, contract) => {
    try {
      const depositLog = receipt.logs.find(log => {
        try {
          const parsed = contract.interface.parseLog(log)
          return parsed.name === 'Deposit'
        } catch {
          return false
        }
      })

      if (depositLog) {
        const parsed = contract.interface.parseLog(depositLog)
        return {
          user: parsed.args.user,
          amount: ethers.utils.formatEther(parsed.args.amount),
          timestamp: new Date(Number(parsed.args.timestamp) * 1000)
        }
      }

      return null
    } catch (error) {
      return null
    }
  }

  const doDeposit = async (amountInPHRS) => {
    if (isProcessing.value) return

    isProcessing.value = true
    error.value = null

    try {
      const provider = new ethers.providers.Web3Provider(walletProvider, chainId)
      const contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, provider)
      const signer = provider.getSigner()
      const contractWithSigner = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer)

      const contractInfo = await getContractInfo()

      if (contractInfo.isPaused) {
        throw new Error(t('topup.contractPaused'))
      }

      const amount = parseFloat(amountInPHRS)
      const minAmount = parseFloat(contractInfo.minDepositAmount)
      const maxAmount = parseFloat(contractInfo.maxDepositAmount)

      if (amount < minAmount) {
        throw new Error(t('topup.amountTooSmall', { minAmount }))
      }

      if (amount > maxAmount) {
        throw new Error(t('topup.amountTooLarge', { maxAmount }))
      }

      const userAddress = await signer.getAddress()
      const userBalance = await provider.getBalance(userAddress)
      const depositAmount = ethers.utils.parseEther(amountInPHRS)

      if (userBalance < depositAmount) {
        throw new Error(t('topup.insufficientBalance'))
      }

      const tx = await contractWithSigner.deposit({
        value: depositAmount
      })

      const receipt = await tx.wait()

      if (receipt.status === 1) {
        const depositEvent = parseDepositEvent(receipt, contract)

        return {
          success: true,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          event: depositEvent
        }
      } else {
        throw new Error(t('topup.transactionFailed'))
      }

    } catch (error) {
      if (error.message.includes('topup.')) {
        error.value = error.message
        throw error
      } else {
        const friendlyError = new Error(t('topup.failed'))
        error.value = friendlyError.message
        throw friendlyError
      }
    } finally {
      isProcessing.value = false
    }
  }

  return {
    doDeposit,
    getContractInfo,
    isProcessing,
    error
  }
} 