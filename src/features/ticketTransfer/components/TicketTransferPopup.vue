<script setup>
import { ref, computed, onMounted } from 'vue'
import { AppInput, TitlePanelColor,AppPopup, ColorButton } from '@/components/common'
import { useTicketTransferPopupStore } from '../stores/ticketTransferPopupStore'
import { useTicketTransfer } from '../composables/useTicketTransfer'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const ticketTransferPopupStore = useTicketTransferPopupStore()
const { remainingLimit, transferTicket, fetchRemainingLimit,} = useTicketTransfer();

const recipient = ref('')
const amount = ref(0)

const canTransfer = computed(() => {
    if(recipient.value === "" || amount.value === 0)  return false;
    if (remainingLimit.value === 0) return false;
  return amount.value > 0 && amount.value <= remainingLimit.value;
});

const handleTransferTickets = async () => {
    if(!canTransfer.value) return;
    await transferTicket(recipient.value, amount.value);
}

onMounted(() => {
  fetchRemainingLimit()
})
</script>

<template>
  <AppPopup
    :isOpen="ticketTransferPopupStore.isTicketTransferShow"
    @close="ticketTransferPopupStore.closeTicketTransferPopup"
  >
    <template #title>
      <TitlePanelColor variant="green" class="title header">{{ $t('ticketTransfer.title') }}</TitlePanelColor>
    </template>

      <div class="popup-content">
        <AppInput class="input" :label="$t('ticketTransfer.recipientLabel')" :placeholder="$t('ticketTransfer.recipientPlaceholder')" v-model="recipient">
          <template #icon>
            <div class="info-wrapper">
              <div class="info">
                <img src="/ui/question-mark.png" alt="info" />
              </div>
              <transition name="fade-slide">
                <div class="info-description">
                  {{ $t('common.recipientAddressInfo') }}
                </div>
              </transition>
            </div>
          </template>
        </AppInput>
        <AppInput class="input" :type="'number'" :label="$t('ticketTransfer.ticketCountLabel')" :placeholder="$t('ticketTransfer.ticketCountPlaceholder')" v-model="amount">
        </AppInput>
        <div class="limit-info">
          {{ $t('ticketTransfer.availableTickets', { current: remainingLimit, max: 6 }) }}
        </div>
        <ColorButton @click="handleTransferTickets" :disabled="!canTransfer">{{ $t('ticketTransfer.transferButton') }}</ColorButton>
      </div>
  </AppPopup>
</template>

<style scoped>



.popup-content {
    padding: calc(var(--base-unit) * 0);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 20);
}

.info {
    transition: transform 0.1s ease;
    padding: calc(var(--base-unit) * 0);
    display: flex;
    justify-content: center;
    align-items: center;
}

.info:active {
    transform: scale(0.9);
}

.info img {
    object-fit: contain;
    width: calc(var(--base-unit) * 20);
    height: calc(var(--base-unit) * 20);
}

.info-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* ensures tooltip appears under icon */
}


.info-description {
  opacity: 0;
  transform: translateY(calc(var(--base-unit) * 0));
  position: absolute;
  top: 100%;
  margin-top: calc(var(--base-unit) * 2);
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: calc(var(--base-unit) * 8);
  font-size: calc(var(--base-unit) * 14);
  padding: calc(var(--base-unit) * 12);
  width: calc(var(--base-unit) * 250);
  box-shadow: 0 calc(var(--base-unit) * 4) calc(var(--base-unit) * 6) rgba(0, 0, 0, 0.3);
  z-index: 10;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.info-wrapper:focus .info-description,
.info-wrapper:hover .info-description {
  opacity: 1;
  transform: translateY(calc(var(--base-unit) * 5));
}

.limit-info {
  color: #6f7b92;
  margin-top: calc(var(--base-unit) * 2);
}

</style>
