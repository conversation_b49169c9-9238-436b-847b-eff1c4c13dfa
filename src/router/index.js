import { createRouter, createWebHistory } from 'vue-router'


// Layouts
const BlankLayout = () => import('@/layouts/BlankLayout.vue') // Used for splash/login
const MainLayout = () => import('@/layouts/MainLayout.vue')   // Main app shell with top/bottom UI

// Shared Views
const SplashScreen = () => import('@/views/shared/SplashScreen.vue') // Entry splash
const Authentication = () => import('@/views/shared/Authentication.vue') // Entry splash

// Section Views: v1
const Home = () => import('@/views/v1/Home.vue')             // /v1
const Referral = () => import('@/views/v1/Referral.vue')       // /v1/referral
const Inventory = () => import('@/views/v1/Inventory.vue')   // /v1/inventory
const Task = () => import('@/views/v1/Task.vue')             // /v1/task
const Jackpot = () => import('@/views/v1/Jackpot.vue')       // /v1/jackpot
const Leaderboard = () => import('@/views/v1/Leaderboard.vue') // /v1/leaderboard


// Profile Routes (v1) 
const Profile = () => import('@/views/v1/profile/index.vue')         // /v1/profile
const ProfileSetting = () => import('@/views/v1/profile/ProfileSetting.vue') // /v1/profile/setting

// Shop
const Shop = () => import('@/views/v1/iap/Shop.vue')
const History = () => import('@/views/v1/iap/History.vue')

const routes = [
  { path: '/', component: BlankLayout, children: [{ path: '', component: SplashScreen }] },
  { path: '/authentication', component: BlankLayout, children: [{ path: '', component: Authentication }] },
  { path: '/v1', component: MainLayout, children: [
    { path: '', component: Home },
    { path: 'referral', component: Referral },
    { path: 'inventory', component: Inventory },
    { path: 'task', component: Task },
    { path: 'jackpot', component: Jackpot },
    { path: 'leaderboard', component: Leaderboard },
    { path: 'shop', component: Shop },
    { path: 'history', component: History },
    {
      path: 'profile',
      component: BlankLayout,
      children: [
        { path: '', component: Profile },
        { path: 'setting', component: ProfileSetting }
      ]
    }
  ]}
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
