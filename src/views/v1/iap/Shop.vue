<script setup>
import { onMounted } from 'vue'
import { TitlePanelColor } from '@/components/common'
import { useProductData, usePayment } from '@/features/iap'
import { useShopStore } from '@/features/iap/stores/shopStore'
import { useRouter } from 'vue-router'
import { ProductSection, ShopPaymentPopup, PaymentContent } from '@/features/iap'
import { useTheme } from '@/themes/useTheme'

const router = useRouter()
const shopStore = useShopStore()
const { specialOfferProducts, timeWrapProducts, boosterProducts } = useProductData()
const { 
  selectedProduct, 
  showPurchaseModal, 
  closePurchaseModal, 
  purchaseProduct, 
  selectPaymentMethod 
} = usePayment()

const { themeName } = useTheme()

onMounted(() => {
  console.log('import.meta.env.PROD', import.meta.env.PROD)
  console.log('import.meta.env.DEV', import.meta.env.DEV)
})

</script>

<template>
  <div class="shop-container">
    

    <TitlePanelColor variant="green" class="shop-title">
      {{ $t('shop.title') }}
    </TitlePanelColor>

    <ProductSection 
      :products="specialOfferProducts" 
      :category-label="$t('shop.categories.specialOffer')"
      @product-click="purchaseProduct"
    />
    
    <ProductSection 
      :products="timeWrapProducts" 
      :category-label="$t('shop.categories.timeWrap')"
      @product-click="purchaseProduct"
    />

    <ProductSection 
      :products="boosterProducts" 
      :category-label="$t('shop.categories.booster')"
      @product-click="purchaseProduct"
    />

    <button 
      class="history-demo-button"
      @click="router.push('/v1/history')"
    >
      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
        <polyline points="14,2 14,8 20,8"></polyline>
        <line x1="16" y1="13" x2="8" y2="13"></line>
        <line x1="16" y1="17" x2="8" y2="17"></line>
        <polyline points="10,9 9,9 8,9"></polyline>
      </svg>
      <span>{{ $t('history.title') }}</span>
    </button>

    <ShopPaymentPopup :isOpen="showPurchaseModal" @close="closePurchaseModal">
      <component :is="PaymentContent" 
        v-if="selectedProduct"
        :phrs-price="themeName === 'gb' ? selectedProduct.pricePhrs : undefined"
        :usd-price="themeName === 'mf' ? selectedProduct.priceUsd : undefined"
        :kaia-price="themeName === 'mf' ? selectedProduct.priceKaia : undefined"
        :processing="shopStore.processing"
        :payment-step="shopStore.paymentStep"
        @select="selectPaymentMethod"
        @close="closePurchaseModal"
      />
    </ShopPaymentPopup>
  </div>
</template>


<style scoped>
.shop-container {
  margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 24);
}

.shop-title {
  margin-bottom: calc(var(--base-unit) * 8);
}

.vip-demo-section {
  display: flex;
  gap: calc(var(--base-unit) * 12);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: calc(var(--base-unit) * 24);
}

.vip-demo-button {
  background: linear-gradient(45deg, #8FCD2D, #6BA32A);
  color: white;
  border: calc(var(--base-unit) * 2) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 10) calc(var(--base-unit) * 16);
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
  text-align: center;
}

.history-demo-button {
  background: #2D7CCD;
  color: white;
  border: 1px solid #D8BEAF;
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 20);
  font-size: calc(var(--base-unit) * 16);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 8);
  box-shadow: 
    0 2px 8px rgba(45, 124, 205, 0.3),
    inset 0 0 0 1px rgba(10, 13, 18, 0.18),
    inset 0 -2px 0 0 rgba(10, 13, 18, 0.05);
  align-self: center;
  /* min-width: calc(var(--base-unit) * 200); */
  width: 100%;
}

.vip-demo-button:hover {
  transform: scale(1.02);
}

.history-demo-button:hover {
  transform: scale(1.02);
  box-shadow: 
    0 4px 12px rgba(45, 124, 205, 0.4),
    inset 0 0 0 1px rgba(10, 13, 18, 0.18),
    inset 0 -2px 0 0 rgba(10, 13, 18, 0.05);
}

.vip-demo-button:active {
  transform: scale(0.98);
}

.history-demo-button:active {
  transform: scale(0.98);
  box-shadow: 
    0 1px 4px rgba(45, 124, 205, 0.3),
    inset 0 0 0 1px rgba(10, 13, 18, 0.18),
    inset 0 -2px 0 0 rgba(10, 13, 18, 0.05);
}


</style> 