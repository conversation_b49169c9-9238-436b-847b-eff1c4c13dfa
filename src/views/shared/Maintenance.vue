<script setup>
import VersionText from '@/components/utility/VersionText.vue';
import { Background } from '@/components/common';

import { onMounted, ref } from 'vue'

// Maintenance information
const maintenanceInfo = ref({
  estimatedEndTime: '2024-01-15T18:00:00Z', // ISO string for end time
  contactEmail: '<EMAIL>',
  contactDiscord: 'https://discord.gg/moofun',
  contactTelegram: 'https://t.me/moofun_support'
})

// Calculate time remaining
const timeRemaining = ref('')
const isMaintenanceActive = ref(true)

const updateTimeRemaining = () => {
  const now = new Date()
  const endTime = new Date(maintenanceInfo.value.estimatedEndTime)
  const diff = endTime - now

  if (diff <= 0) {
    timeRemaining.value = 'Maintenance completed!'
    isMaintenanceActive.value = false
    return
  }

  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    timeRemaining.value = `${hours}h ${minutes}m remaining`
  } else {
    timeRemaining.value = `${minutes}m remaining`
  }
}

onMounted(() => {
  updateTimeRemaining()
  // Update countdown every minute
  setInterval(updateTimeRemaining, 60000)
})
</script>

<template>
  <div class="container">
    <Background class="background-image" :imagePath="'/img/intro.png'"/>
    <VersionText />

    <div class="maintenance-content">
      <div class="maintenance-icon">
        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#FFD700"/>
          <path d="M19 14L17.5 15.5L19 17L17.5 18.5L19 20L17.5 21.5L16 20L14.5 21.5L13 20L11.5 21.5L10 20L8.5 21.5L7 20L5.5 21.5L4 20L2.5 21.5L1 20L2.5 18.5L1 17L2.5 15.5L1 14L2.5 12.5L1 11L2.5 9.5L1 8L2.5 6.5L1 5L2.5 3.5L1 2L2.5 0.5L4 2L5.5 0.5L7 2L8.5 0.5L10 2L11.5 0.5L13 2L14.5 0.5L16 2L17.5 0.5L19 2L20.5 0.5L22 2L20.5 3.5L22 5L20.5 6.5L22 8L20.5 9.5L22 11L20.5 12.5L22 14L20.5 15.5L19 14Z" fill="#FFD700"/>
        </svg>
      </div>

      <h1 class="maintenance-title">Under Maintenance</h1>
      
      <p class="maintenance-description">
        We're currently performing scheduled maintenance to improve your gaming experience.
      </p>

      <div class="time-remaining">
        <span class="time-label">Estimated completion:</span>
        <span class="time-value">{{ timeRemaining }}</span>
      </div>

      <div class="contact-section">
        <h3 class="contact-title">For detailed updates:</h3>
        
        <div class="contact-methods">
          <a :href="`mailto:${maintenanceInfo.contactEmail}`" class="contact-link email">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
            </svg>
            {{ maintenanceInfo.contactEmail }}
          </a>
          
          <a :href="maintenanceInfo.contactDiscord" target="_blank" class="contact-link discord">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
            </svg>
            Discord
          </a>
          
          <a :href="maintenanceInfo.contactTelegram" target="_blank" class="contact-link telegram">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.8c-.15 1.58-.8 5.42-1.13 7.19-.14.75-.42 1-.68 1.03-.58.05-1.02-.38-1.58-.75-.88-.58-1.38-.94-2.23-1.5-.99-.65-.35-1.01.22-1.59.15-.15 2.71-2.48 2.76-2.69a.2.2 0 0 0-.05-.18c-.06-.05-.14-.03-.21-.02-.09.02-1.49.95-4.22 2.79-.4.27-.76.41-1.08.4-.36-.01-1.04-.2-1.55-.37-.63-.2-1.12-.31-1.08-.66.02-.18.27-.36.74-.55 2.92-1.27 4.86-2.11 5.83-2.51 2.78-1.16 3.35-1.36 3.73-1.36.08 0 .27.02.39.12.1.08.13.19.14.27-.01.06-.01.13-.02.2z"/>
            </svg>
            Telegram
          </a>
        </div>
      </div>

      <div class="thank-you">
        <p>Thank you for your patience!</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.container {
  position: relative;
  width: 100%;
  height: var(--app-height);
  overflow: hidden;
  background-color: #2c3757;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: brightness(0.2) opacity(0.8) saturate(0.5);
  z-index: 1;
}

.maintenance-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 500px;
  padding: 0 20px;
  color: white;
}

.maintenance-icon {
  margin-bottom: 24px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.maintenance-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 16px;
  color: #FFD700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.maintenance-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 32px;
  color: #E0E0E0;
}

.time-remaining {
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid #FFD700;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
  backdrop-filter: blur(10px);
}

.time-label {
  display: block;
  font-size: 0.9rem;
  color: #FFD700;
  margin-bottom: 8px;
}

.time-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #FFD700;
}

.contact-section {
  margin-bottom: 32px;
}

.contact-title {
  font-size: 1.2rem;
  margin-bottom: 16px;
  color: #E0E0E0;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.contact-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 200px;
  justify-content: center;
}

.contact-link:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.contact-link.email:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: #FFD700;
}

.contact-link.discord:hover {
  background: rgba(114, 137, 218, 0.2);
  border-color: #7289DA;
}

.contact-link.telegram:hover {
  background: rgba(0, 136, 204, 0.2);
  border-color: #0088CC;
}

.thank-you {
  margin-top: 24px;
  padding: 16px;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.thank-you p {
  margin: 0;
  color: #FFD700;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .maintenance-title {
    font-size: 2rem;
  }
  
  .maintenance-description {
    font-size: 1rem;
  }
  
  .time-value {
    font-size: 1.3rem;
  }
  
  .contact-link {
    min-width: 180px;
    padding: 10px 16px;
  }
}
</style> 