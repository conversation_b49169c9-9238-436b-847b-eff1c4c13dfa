/**
 * Platform Detection Utility
 * Detects the current platform environment (LIFF, Web, Telegram, etc.)
 */

// Platform types
export const PLATFORM_TYPES = {
  LIFF: 'liff',
  WEB: 'web',
  TELEGRAM: 'telegram',
  UNKNOWN: 'unknown'
}


/**
 * Detect if the app is running in LIFF (Line)
 * @returns {boolean}
 */
export const isInLIFF = () => {
  if(typeof window === 'undefined') {
    return false
  }

  // Check for LIFF SDK
  if (window.liff) {
    console.log('window.liff', window.liff)
    return true
  }
  
  // Check for LIFF environment variables
  if (window.liff?.isInClient()) {
    console.log('window.liff?.isInClient()', window.liff?.isInClient())
    return true
  }
  
  // Check URL for LIFF domain
  if (window.location.hostname.includes('liff.line.me')) {
    return true
  }
  
  // Check for LIFF query parameters
  if (window.location.search.includes('liff.state')) {
    return true
  }
  
  return false
}

/**
 * Detect if the app is running in Telegram Web App
 * @returns {boolean}
 */
export const isInTelegram = () => {
  if(typeof window === 'undefined') {
    return false
  }

  // Check for Telegram Web App SDK
  if (window.Telegram?.WebApp) {
    return true
  }
  
  // Check for Telegram Web App environment
  if (window.Telegram?.WebApp?.initData) {
    return true
  }
  
  // Check URL for Telegram domain
  if (window.location.hostname.includes('t.me')) {
    return true
  }
  
  // Check for Telegram query parameters
  if (window.location.search.includes('tgWebApp')) {
    return true
  }
  
  return false
}

/**
 * Detect if the app is running in Kaia Web environment
 * @returns {boolean}
 */
export const isInKaia = () => {
  // Check for Kaia-specific environment variables
  if (import.meta.env.VITE_KAIA_ENVIRONMENT) {
    return true
  }

  if(typeof window === 'undefined') {
    return false
  }
  
  // Check for Kaia wallet provider
  if (window.kaia) {
    return true
  }
  
  // Check for Kaia-specific query parameters
  if (window.location.search.includes('kaia')) {
    return true
  }
  
  // Check if DappPortal SDK is available (indicates Kaia environment)
  if (window.DappPortalSDK) {
    return true
  }
  
  return false
}

/**
 * Detect the current platform
 * @returns {string} Platform type (liff, web, telegram, unknown)
 */
export const detectPlatform = () => {
  if (isInLIFF()) {
    return PLATFORM_TYPES.LIFF
  }
  
  if (isInTelegram()) {
    return PLATFORM_TYPES.TELEGRAM
  }
  
  if (isInKaia()) {
    return PLATFORM_TYPES.WEB
  }
  
  return PLATFORM_TYPES.UNKNOWN
}

/**
 * Get the default platform for referral links based on current environment
 * @returns {string} Default platform ('telegram', 'kaia', 'line')
 */
export const getDefaultReferralPlatform = () => {
  const platform = detectPlatform()
  
  switch (platform) {
    case PLATFORM_TYPES.LIFF:
      return 'line'
    case PLATFORM_TYPES.TELEGRAM:
      return 'telegram'
    case PLATFORM_TYPES.WEB:
    default:
      return 'kaia'
  }
}

// Export platform types for use in other modules
export default {
  PLATFORM_TYPES,
  isInLIFF,
  isInTelegram,
  isInKaia,
  detectPlatform,
  getDefaultReferralPlatform
} 