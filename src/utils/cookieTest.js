/**
 * Cookie配置测试工具
 * 用于验证API请求是否正确携带cookie
 */

import fetch from '@/lib/fetch'

/**
 * 测试API请求是否携带cookie
 * @returns {Promise<boolean>} 返回测试结果
 */
export const testCookieSupport = async () => {
  try {
    console.log('🍪 测试API请求cookie支持...')
    
    // 检查axios实例配置
    const axiosConfig = fetch.defaults
    console.log('📋 Axios配置:', {
      baseURL: axiosConfig.baseURL,
      withCredentials: axiosConfig.withCredentials,
      headers: axiosConfig.headers
    })
    
    if (axiosConfig.withCredentials) {
      console.log('✅ Cookie支持已启用 (withCredentials: true)')
      return true
    } else {
      console.log('❌ Cookie支持未启用 (withCredentials: false)')
      return false
    }
  } catch (error) {
    console.error('❌ Cookie配置测试失败:', error)
    return false
  }
}

/**
 * 在浏览器控制台中运行cookie测试
 * 使用方法: 在浏览器控制台中输入 window.testCookies()
 */
export const setupCookieTest = () => {
  if (typeof window !== 'undefined') {
    window.testCookies = testCookieSupport
    console.log('🔧 Cookie测试工具已设置，在控制台中输入 window.testCookies() 来测试')
  }
}
