/**
 * Cookie辅助工具
 * 用于设置、获取和测试cookie
 */

/**
 * 设置cookie
 * @param {string} name - cookie名称
 * @param {string} value - cookie值
 * @param {number} days - 过期天数
 * @param {string} path - 路径，默认为'/'
 * @param {string} domain - 域名，可选
 * @param {boolean} secure - 是否只在HTTPS下发送
 * @param {string} sameSite - SameSite属性：'Strict', 'Lax', 'None'
 */
export const setCookie = (name, value, days = 7, path = '/', domain = null, secure = false, sameSite = 'Lax') => {
  let cookieString = `${name}=${encodeURIComponent(value)}`
  
  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000))
    cookieString += `; expires=${date.toUTCString()}`
  }
  
  cookieString += `; path=${path}`
  
  if (domain) {
    cookieString += `; domain=${domain}`
  }
  
  if (secure) {
    cookieString += `; secure`
  }
  
  cookieString += `; SameSite=${sameSite}`
  
  document.cookie = cookieString
  console.log(`🍪 设置cookie: ${cookieString}`)
}

/**
 * 获取cookie值
 * @param {string} name - cookie名称
 * @returns {string|null} cookie值或null
 */
export const getCookie = (name) => {
  const nameEQ = name + "="
  const ca = document.cookie.split(';')
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') c = c.substring(1, c.length)
    if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length))
  }
  return null
}

/**
 * 删除cookie
 * @param {string} name - cookie名称
 * @param {string} path - 路径
 * @param {string} domain - 域名
 */
export const deleteCookie = (name, path = '/', domain = null) => {
  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`
  
  if (domain) {
    cookieString += `; domain=${domain}`
  }
  
  document.cookie = cookieString
  console.log(`🗑️ 删除cookie: ${name}`)
}

/**
 * 设置测试cookie并验证API请求
 */
export const setupTestCookie = () => {
  // 设置一个测试cookie
  setCookie('test_cookie', 'test_value_' + Date.now(), 1)
  
  // 设置一个会话cookie（用于API认证测试）
  setCookie('session_id', 'session_' + Date.now(), 1)
  
  console.log('✅ 测试cookie已设置')
  console.log('📋 当前所有cookie:', document.cookie)
  
  return {
    testCookie: getCookie('test_cookie'),
    sessionId: getCookie('session_id')
  }
}

/**
 * 检查cookie是否在API请求中被发送
 */
export const checkCookieInRequests = () => {
  console.log('🔍 检查当前cookie状态:')
  console.log('📋 所有cookie:', document.cookie)
  console.log('🧪 测试cookie:', getCookie('test_cookie'))
  console.log('🔐 会话cookie:', getCookie('session_id'))
  
  // 检查域名信息
  console.log('🌐 域名信息:', {
    hostname: window.location.hostname,
    protocol: window.location.protocol,
    port: window.location.port,
    origin: window.location.origin
  })
}
