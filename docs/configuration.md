# Configuration Guide

This document explains how to configure the application URLs and environment variables, including platform detection.

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# API Configuration
VITE_API_BASE_URL=https://api.moofun.app

# App Domain Configuration
VITE_APP_DOMAIN=https://moofun.app

# Telegram Bot Configuration
VITE_TELEGRAM_BOT_USERNAME=moofun_bot

# Line LIFF Configuration
VITE_LINE_LIFF_ID=moofun

# Social Media Configuration
VITE_TELEGRAM_CHANNEL=https://t.me/MooFun
VITE_TWITTER_HANDLE=https://x.com/MooFunGame

# Platform Detection (Optional)
VITE_KAIA_ENVIRONMENT=true
```

## Platform Detection

The application includes a comprehensive platform detection system that automatically identifies the current environment:

### Supported Platforms

- **LIFF (Line)**: `liff.line.me` domains, LIFF SDK presence
- **Telegram**: `t.me` domains, Telegram Web App SDK
- **Kaia (Web)**: DappPortal SDK, Kaia-specific environment
- **Unknown**: Fallback for unrecognized environments

### Detection Methods

The system uses multiple detection methods:

1. **SDK Detection**: Checks for platform-specific SDKs (`window.liff`, `window.Telegram.WebApp`)
2. **Domain Detection**: Analyzes the current URL hostname
3. **Query Parameters**: Looks for platform-specific URL parameters
4. **Environment Variables**: Checks for platform-specific environment variables

### Usage Examples

#### Basic Platform Detection
```javascript
import { detectPlatform, getPlatformInfo } from '@/utils/platformDetection'

// Detect current platform
const platform = detectPlatform() // 'liff', 'telegram', 'web', or 'unknown'

// Get detailed platform information
const info = getPlatformInfo()
console.log(info.platform) // Current platform
console.log(info.isLIFF)   // Boolean
console.log(info.isTelegram) // Boolean
console.log(info.isWeb)    // Boolean
```

#### Vue Composable Usage
```javascript
import { usePlatform } from '@/composables/usePlatform'

// In your Vue component
const {
  platform,
  isInLIFF,
  isInTelegram,
  isInKaia,
  supportsWallet,
  supportsPayment,
  supportsSharing,
  defaultReferralPlatform
} = usePlatform()

// Reactive platform checks
if (isInLIFF.value) {
  // LIFF-specific logic
}

if (supportsWallet.value) {
  // Show wallet features
}
```

#### Platform-Aware URL Generation
```javascript
import { generateReferralLink } from '@/utils/config'

// Automatic platform detection
const link = generateReferralLink('ABC123', 'auto')
// In LIFF: https://liff.line.me/moofun?ref=ABC123
// In Telegram: https://t.me/moofun_bot?startapp=r_ABC123
// In Kaia: https://moofun.app/referral?ref=ABC123
```

## URL Configuration

The application now uses a centralized configuration system located in `src/utils/config.js` that handles:

### Base URLs
- **Development**: Automatically uses `http://localhost:3000`
- **Production**: Uses `window.location.origin` or `VITE_APP_DOMAIN`

### Referral Links
- **Telegram**: `https://t.me/{bot_username}?startapp=r_{code}`
- **Kaia**: `{app_domain}/referral?ref={code}`
- **Line**: `https://liff.line.me/{liff_id}?ref={code}`

### Boost Links
- **Telegram**: `https://t.me/{bot_username}?startapp=b_{code}`

### Task Links
- **Base**: `{base_url}/v1/task`
- **With Code**: `{base_url}/v1/task?task={code}`

### Social Media Links
- **Telegram Channel**: `{VITE_TELEGRAM_CHANNEL}` (defaults to `https://t.me/MooFun`)
- **Twitter/X**: `{VITE_TWITTER_HANDLE}` (defaults to `https://x.com/MooFunGame`)

## Usage Examples

### In Components
```javascript
import { generateReferralLink, generateBoostLink, generateTaskLink, getSocialLinks } from '@/utils/config'

// Generate referral link for Telegram
const telegramLink = generateReferralLink('ABC123', 'telegram')

// Generate boost link
const boostLink = generateBoostLink('BOOST456')

// Generate task link
const taskLink = generateTaskLink('TASK789')

// Get social media links
const socialLinks = getSocialLinks()
console.log(socialLinks.telegram) // https://t.me/MooFun
console.log(socialLinks.twitter)  // https://x.com/MooFunGame

// Get platform configuration
const platformConfig = getPlatformConfig()
console.log(platformConfig.platform) // Current platform
console.log(platformConfig.supportsWallet) // Feature support
```

### Environment Detection
```javascript
import { config } from '@/utils/config'

if (config.isDevelopment) {
  // Development-specific logic
}

if (config.isProduction) {
  // Production-specific logic
}

// Platform-specific logic
if (config.platform.isInLIFF) {
  // LIFF-specific features
}

if (config.platform.supportsWallet) {
  // Show wallet functionality
}
```

## Platform-Specific Features

### LIFF (Line)
- ✅ Wallet integration
- ✅ Payment processing
- ✅ Social sharing
- ✅ Line-specific UI adaptations

### Telegram
- ✅ Social sharing
- ✅ Telegram Web App integration
- ❌ Wallet integration (limited)
- ❌ Payment processing (limited)

### Kaia (Web)
- ✅ Full wallet integration
- ✅ Payment processing
- ✅ All web features
- ✅ DappPortal SDK integration

## Benefits

1. **Environment Awareness**: URLs automatically adapt to development/production environments
2. **Platform Detection**: Automatic detection of LIFF, Telegram, and Kaia environments
3. **Centralized Configuration**: All URL generation logic is in one place
4. **Easy Maintenance**: Change URLs by updating environment variables
5. **Type Safety**: Configuration functions are properly typed
6. **Flexibility**: Support for multiple platforms and environments
7. **Social Media Integration**: Configurable social media links for tasks
8. **Feature Detection**: Automatic detection of platform capabilities

## Migration

The following files have been updated to use the new configuration system:

- `src/features/referral/composables/useReferralCode.js`
- `src/features/openChest/components/ChestShareView.vue`
- `src/features/task/composables/useTasks.js`

All hardcoded URLs have been replaced with calls to the centralized configuration functions.

## Environment-Specific Behavior

### Development Environment
- Base URLs default to `http://localhost:3000`
- Social media links use default values
- Platform detection debug info is shown
- Easy local testing and development

### Production Environment
- Base URLs use `window.location.origin` or `VITE_APP_DOMAIN`
- Social media links can be customized via environment variables
- Platform detection runs silently
- Supports multiple deployment environments

## Debug Component

For development, you can add the `PlatformInfo` component to see platform detection in action:

```vue
<template>
  <div>
    <!-- Your app content -->
    <PlatformInfo />
  </div>
</template>

<script setup>
import PlatformInfo from '@/components/common/PlatformInfo.vue'
</script>
```

This component will show the detected platform and its capabilities in the top-right corner during development. 