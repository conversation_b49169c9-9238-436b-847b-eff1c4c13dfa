# 环境变量配置指南

本文档详细说明了 MooFun 项目所需的所有环境变量配置。

## 📋 环境变量清单

### 🔧 核心配置

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `VITE_API_BASE_URL` | ✅ | 无 | 后端 API 的基础 URL |
| `VITE_APP_DOMAIN` | ✅ | `https://moofun.app` | 应用的主域名 |

### 🤖 平台集成

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `VITE_TELEGRAM_BOT_USERNAME` | ✅ | `moofun_bot` | Telegram 机器人用户名 |
| `VITE_LINE_LIFF_ID` | ✅ | `moofun` | Line LIFF 应用 ID |
| `VITE_DAPP_CLIENT_ID` | ✅ | 无 | Kaia DApp Portal 客户端 ID |

### 📱 社交媒体

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `VITE_TELEGRAM_CHANNEL` | ❌ | `https://t.me/MooFun` | Telegram 频道链接 |
| `VITE_TWITTER_HANDLE` | ❌ | `https://x.com/MooFunGame` | Twitter/X 账号链接 |

### 🔍 平台检测

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `VITE_KAIA_ENVIRONMENT` | ❌ | `false` | 强制启用 Kaia 环境检测 |

## 🚀 快速开始

### 1. 复制环境变量模板

```bash
cp .env.example .env
```

### 2. 编辑环境变量

根据您的实际配置修改 `.env` 文件中的值。

### 3. 重启开发服务器

```bash
npm run dev
```

## 🌍 不同环境配置

### 开发环境 (.env.development)

```bash
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_DOMAIN=http://localhost:3000
VITE_TELEGRAM_BOT_USERNAME=moofun_dev_bot
VITE_LINE_LIFF_ID=dev-liff-id
VITE_DAPP_CLIENT_ID=dev-client-id
```

### 测试环境 (.env.staging)

```bash
VITE_API_BASE_URL=https://api-staging.moofun.app
VITE_APP_DOMAIN=https://staging.moofun.app
VITE_TELEGRAM_BOT_USERNAME=moofun_staging_bot
VITE_LINE_LIFF_ID=staging-liff-id
VITE_DAPP_CLIENT_ID=staging-client-id
```

### 生产环境 (.env.production)

```bash
VITE_API_BASE_URL=https://api.moofun.app
VITE_APP_DOMAIN=https://moofun.app
VITE_TELEGRAM_BOT_USERNAME=moofun_bot
VITE_LINE_LIFF_ID=production-liff-id
VITE_DAPP_CLIENT_ID=production-client-id
```

## 📝 详细说明

### VITE_API_BASE_URL
- **用途**: 配置后端 API 的基础 URL
- **使用位置**: `src/lib/fetch.ts`
- **示例**: `https://api.moofun.app`

### VITE_APP_DOMAIN
- **用途**: 配置应用的主域名，用于生成分享链接
- **使用位置**: `src/utils/config.js`
- **示例**: `https://moofun.app`

### VITE_TELEGRAM_BOT_USERNAME
- **用途**: Telegram 机器人的用户名，用于生成 Telegram 分享链接
- **使用位置**: `src/utils/config.js`
- **示例**: `moofun_bot`

### VITE_LINE_LIFF_ID
- **用途**: Line LIFF 应用的 ID，用于 Line 平台集成
- **使用位置**: `src/main.js`, `src/utils/config.js`
- **示例**: `1234567890-abcdefgh`

### VITE_DAPP_CLIENT_ID
- **用途**: Kaia DApp Portal 的客户端 ID，用于钱包集成
- **使用位置**: `src/features/auth/composables/useDappPortalSdk.js`
- **获取方式**: 从 Kaia DApp Portal 注册获得

## ⚠️ 注意事项

1. **环境变量前缀**: 所有客户端环境变量必须以 `VITE_` 开头
2. **敏感信息**: 不要在环境变量中存储敏感的私钥或密码
3. **版本控制**: `.env` 文件不应提交到版本控制系统
4. **Node 版本**: 确保使用 Node.js 22 版本

## 🔧 故障排除

### 环境变量未生效
1. 确认变量名以 `VITE_` 开头
2. 重启开发服务器
3. 检查 `.env` 文件是否在项目根目录

### LIFF 初始化失败
1. 确认 `VITE_LINE_LIFF_ID` 配置正确
2. 检查 Line 开发者控制台中的 LIFF 应用设置

### DApp Portal 连接失败
1. 确认 `VITE_DAPP_CLIENT_ID` 配置正确
2. 检查 Kaia 网络连接状态

## 📚 相关文档

- [Vite 环境变量文档](https://vitejs.dev/guide/env-and-mode.html)
- [Line LIFF 文档](https://developers.line.biz/en/docs/liff/)
- [Kaia DApp Portal 文档](https://docs.kaia.io/)
