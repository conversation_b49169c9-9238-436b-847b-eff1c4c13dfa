<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Safari Test Page</title>
    <style>
        :root {
            --vh: 1vh;
            --app-width: 100vw;
            --app-height: 100vh;
            --base-unit: calc(var(--app-width) / 375);
        }

        /* Safari viewport height fix */
        @supports (-webkit-appearance: none) {
            :root {
                --app-height: 100vh;
                --app-height: calc(var(--vh, 1vh) * 100);
            }
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            -webkit-text-size-adjust: 100%;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            width: 100%;
            height: var(--app-height);
            min-height: var(--app-height);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px;
            box-sizing: border-box;
        }

        /* Safari specific fixes */
        @supports (-webkit-appearance: none) {
            .container {
                min-height: -webkit-fill-available;
            }
            
            body {
                position: relative;
                overflow-x: hidden;
            }
            
            * {
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
                -webkit-transform: translate3d(0, 0, 0);
                transform: translate3d(0, 0, 0);
            }
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .test-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .test-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .info {
            margin-top: 2rem;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .status {
            margin-top: 1rem;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .success {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .error {
            background: rgba(244, 67, 54, 0.3);
            color: #F44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Safari 兼容性测试</h1>
        <p>这是一个简单的测试页面，用于验证 Safari 浏览器的兼容性</p>
        
        <button class="test-button" onclick="runTests()">运行测试</button>
        
        <div class="info">
            <div><strong>浏览器:</strong> <span id="browser"></span></div>
            <div><strong>视口高度:</strong> <span id="viewport-height"></span></div>
            <div><strong>CSS 变量支持:</strong> <span id="css-vars"></span></div>
            <div><strong>JavaScript 状态:</strong> <span id="js-status"></span></div>
        </div>
        
        <div id="test-results"></div>
    </div>

    <script>
        // Safari viewport height fix
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
            document.getElementById('viewport-height').textContent = `${window.innerHeight}px (--vh: ${vh}px)`;
        }

        // Set initial viewport height
        setViewportHeight();

        // Update viewport height on resize and orientation change
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setViewportHeight, 100);
        });

        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browser = 'Unknown';
            
            if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
                browser = 'Safari';
            } else if (userAgent.includes('Chrome')) {
                browser = 'Chrome';
            } else if (userAgent.includes('Firefox')) {
                browser = 'Firefox';
            }
            
            return browser;
        }

        function testCSSVariables() {
            try {
                const testElement = document.createElement('div');
                testElement.style.setProperty('--test-var', 'test');
                const value = getComputedStyle(testElement).getPropertyValue('--test-var');
                return value.trim() === 'test';
            } catch (e) {
                return false;
            }
        }

        function runTests() {
            const results = [];
            
            // Test 1: Basic JavaScript
            try {
                const testArray = [1, 2, 3];
                const doubled = testArray.map(x => x * 2);
                results.push({ name: 'JavaScript 基础功能', status: 'success', message: '正常' });
            } catch (e) {
                results.push({ name: 'JavaScript 基础功能', status: 'error', message: e.message });
            }
            
            // Test 2: CSS Variables
            const cssVarsSupported = testCSSVariables();
            results.push({ 
                name: 'CSS 变量支持', 
                status: cssVarsSupported ? 'success' : 'error', 
                message: cssVarsSupported ? '支持' : '不支持' 
            });
            
            // Test 3: Viewport units
            try {
                const testDiv = document.createElement('div');
                testDiv.style.height = '100vh';
                document.body.appendChild(testDiv);
                const height = testDiv.offsetHeight;
                document.body.removeChild(testDiv);
                results.push({ 
                    name: 'Viewport 单位', 
                    status: height > 0 ? 'success' : 'error', 
                    message: `100vh = ${height}px` 
                });
            } catch (e) {
                results.push({ name: 'Viewport 单位', status: 'error', message: e.message });
            }
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>测试结果:</h3>' + 
                results.map(result => 
                    `<div class="status ${result.status}">
                        ${result.name}: ${result.message}
                    </div>`
                ).join('');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('browser').textContent = detectBrowser();
            document.getElementById('css-vars').textContent = testCSSVariables() ? '支持' : '不支持';
            document.getElementById('js-status').textContent = '正常运行';
            setViewportHeight();
        });
    </script>
</body>
</html>
