<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Safari 测试页面</title>
    <style>
        :root {
            --vh: 1vh;
            --app-width: 100vw;
            --app-height: 100vh;
        }

        /* Safari viewport height fix */
        @supports (-webkit-appearance: none) and (not (display: -ms-grid)) {
            :root {
                --app-height: calc(var(--vh, 1vh) * 100);
            }
        }

        /* Fallback for older Safari versions */
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            :root {
                --app-height: 100vh;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            min-height: 100vh;
            height: 100vh;
            position: relative;
            -webkit-text-size-adjust: 100%;
            -webkit-font-smoothing: antialiased;
            margin: 0;
            padding: 0;
        }

        .container {
            width: var(--app-width);
            height: var(--app-height);
            min-height: var(--app-height);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px;
            position: relative;
        }

        /* Safari specific fixes */
        @supports (-webkit-appearance: none) {
            .container {
                min-height: -webkit-fill-available;
            }
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .success {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .error {
            background: rgba(244, 67, 54, 0.3);
            color: #F44336;
        }

        button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            font-size: 1rem;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Safari 兼容性测试</h1>
        
        <div class="info">
            <div><strong>浏览器:</strong> <span id="browser">检测中...</span></div>
            <div><strong>视口尺寸:</strong> <span id="viewport">检测中...</span></div>
            <div><strong>CSS 变量:</strong> <span id="css-vars">检测中...</span></div>
            <div><strong>当前时间:</strong> <span id="time">检测中...</span></div>
        </div>

        <button onclick="runTests()">🔍 运行测试</button>
        <button onclick="location.href='/'">🏠 返回主页</button>
        
        <div id="test-results"></div>
    </div>

    <script>
        // Safari viewport height fix
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', vh + 'px');
            
            // Update display
            document.getElementById('viewport').textContent = 
                window.innerWidth + 'x' + window.innerHeight + ' (--vh: ' + vh + 'px)';
        }

        function detectBrowser() {
            const ua = navigator.userAgent;
            if (ua.includes('Safari') && !ua.includes('Chrome')) {
                return 'Safari ' + (ua.match(/Version\/([0-9.]+)/) || ['', '未知'])[1];
            } else if (ua.includes('Chrome')) {
                return 'Chrome';
            } else if (ua.includes('Firefox')) {
                return 'Firefox';
            }
            return '未知浏览器';
        }

        function testCSSVariables() {
            try {
                const testEl = document.createElement('div');
                testEl.style.setProperty('--test', 'works');
                const value = getComputedStyle(testEl).getPropertyValue('--test');
                return value.trim() === 'works';
            } catch (e) {
                return false;
            }
        }

        function runTests() {
            const results = [];
            
            // Test 1: Basic JavaScript
            try {
                const test = [1,2,3].map(x => x * 2);
                results.push({
                    name: 'JavaScript 基础功能',
                    status: test.length === 3 ? 'success' : 'error',
                    message: test.length === 3 ? '✅ 正常' : '❌ 异常'
                });
            } catch (e) {
                results.push({
                    name: 'JavaScript 基础功能',
                    status: 'error',
                    message: '❌ ' + e.message
                });
            }
            
            // Test 2: CSS Variables
            const cssVars = testCSSVariables();
            results.push({
                name: 'CSS 变量支持',
                status: cssVars ? 'success' : 'error',
                message: cssVars ? '✅ 支持' : '❌ 不支持'
            });
            
            // Test 3: Viewport calculation
            const vh = window.innerHeight * 0.01;
            results.push({
                name: 'Viewport 计算',
                status: vh > 0 ? 'success' : 'error',
                message: vh > 0 ? '✅ 正常 (' + vh + 'px)' : '❌ 异常'
            });
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>🔬 测试结果:</h3>' + 
                results.map(r => 
                    '<div class="test-result ' + r.status + '">' + r.name + ': ' + r.message + '</div>'
                ).join('');
        }

        // Initialize
        function init() {
            document.getElementById('browser').textContent = detectBrowser();
            document.getElementById('css-vars').textContent = testCSSVariables() ? '✅ 支持' : '❌ 不支持';
            document.getElementById('time').textContent = new Date().toLocaleTimeString();
            setViewportHeight();
        }

        // Set up event listeners
        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setViewportHeight, 200);
        });

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }

        // Safari-specific debugging
        if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
            console.log('🦁 Safari 检测到!');
            console.log('User Agent:', navigator.userAgent);
            console.log('视口尺寸:', window.innerWidth, 'x', window.innerHeight);
            console.log('设备像素比:', window.devicePixelRatio);
        }
    </script>
</body>
</html>
